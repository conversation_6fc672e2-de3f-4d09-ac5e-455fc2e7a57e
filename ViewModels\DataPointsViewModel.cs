using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using DNP3Editor.Models;
using DNP3Editor.Generated;

namespace DNP3Editor.ViewModels;

public partial class DataPointsViewModel : ObservableObject
{
    private readonly DnpDataPointsListType _dataPointsList;

    [ObservableProperty]
    private DataPointType selectedDataPointType = DataPointType.BinaryInput;

    [ObservableProperty]
    private object? selectedDataPoint;

    public ObservableCollection<BinaryInputPointType> BinaryInputPoints { get; } = [];
    public ObservableCollection<AnalogInputPointType> AnalogInputPoints { get; } = [];
    public ObservableCollection<BinaryOutputPointType> BinaryOutputPoints { get; } = [];
    public ObservableCollection<AnalogOutputPointType> AnalogOutputPoints { get; } = [];
    public ObservableCollection<CounterPointType> CounterPoints { get; } = [];

    public DataPointsViewModel(DnpDataPointsListType dataPointsList)
    {
        _dataPointsList = dataPointsList;
        LoadDataPoints();
    }

    [RelayCommand]
    private void AddDataPoint()
    {
        switch (SelectedDataPointType)
        {
            case DataPointType.BinaryInput:
                AddBinaryInputPoint();
                break;
            case DataPointType.AnalogInput:
                AddAnalogInputPoint();
                break;
            case DataPointType.BinaryOutput:
                AddBinaryOutputPoint();
                break;
            case DataPointType.AnalogOutput:
                AddAnalogOutputPoint();
                break;
            case DataPointType.Counter:
                AddCounterPoint();
                break;
        }
    }

    [RelayCommand]
    private void DeleteDataPoint()
    {
        if (SelectedDataPoint == null) return;

        switch (SelectedDataPoint)
        {
            case BinaryInputPointType binaryInput:
                BinaryInputPoints.Remove(binaryInput);
                break;
            case AnalogInputPointType analogInput:
                AnalogInputPoints.Remove(analogInput);
                break;
            // Add other types...
        }

        SaveDataPoints();
    }

    [RelayCommand]
    private void DuplicateDataPoint()
    {
        if (SelectedDataPoint == null) return;

        // TODO: Implement duplication logic
    }

    private void LoadDataPoints()
    {
        // Load binary input points
        if (_dataPointsList.BinaryInputPoints?.DataPoints != null)
        {
            BinaryInputPoints.Clear();
            foreach (var point in _dataPointsList.BinaryInputPoints.DataPoints)
            {
                BinaryInputPoints.Add(point);
            }
        }

        // Load analog input points
        if (_dataPointsList.AnalogInputPoints?.DataPoints != null)
        {
            AnalogInputPoints.Clear();
            foreach (var point in _dataPointsList.AnalogInputPoints.DataPoints)
            {
                AnalogInputPoints.Add(point);
            }
        }

        // Load other point types...
    }

    private void SaveDataPoints()
    {
        // Update binary input points
        if (_dataPointsList.BinaryInputPoints == null)
        {
            _dataPointsList.BinaryInputPoints = new BinaryInputPointsType();
        }
        _dataPointsList.BinaryInputPoints.DataPoints.Clear();
        foreach (var point in BinaryInputPoints)
        {
            _dataPointsList.BinaryInputPoints.DataPoints.Add(point);
        }

        // Update analog input points
        if (_dataPointsList.AnalogInputPoints == null)
        {
            _dataPointsList.AnalogInputPoints = new AnalogInputPointsType();
        }
        _dataPointsList.AnalogInputPoints.DataPoints.Clear();
        foreach (var point in AnalogInputPoints)
        {
            _dataPointsList.AnalogInputPoints.DataPoints.Add(point);
        }

        // Update other point types...
    }

    private void AddBinaryInputPoint()
    {
        var newIndex = BinaryInputPoints.Count > 0 ?
            BinaryInputPoints.Max(p => int.TryParse(p.Index, out var idx) ? idx : 0) + 1 : 0;
        
        var newPoint = new BinaryInputPointType
        {
            Index = newIndex.ToString(),
            Name = $"BinaryInput_{newIndex}",
            Description = "New binary input point",
            ChangeEventClass = EventClassType.One,
            IncludedInClass0Response = IncludedInClass0ResponseType.Always
        };

        BinaryInputPoints.Add(newPoint);
        SaveDataPoints();
    }

    private void AddAnalogInputPoint()
    {
        var newIndex = AnalogInputPoints.Count > 0 ?
            AnalogInputPoints.Max(p => int.TryParse(p.Index, out var idx) ? idx : 0) + 1 : 0;
        
        var newPoint = new AnalogInputPointType
        {
            Index = newIndex.ToString(),
            Name = $"AnalogInput_{newIndex}",
            Description = "New analog input point",
            ChangeEventClass = EventClassType.One,
            IncludedInClass0Response = IncludedInClass0ResponseType.Always,
            Units = "Units",
            ScaleFactor = 1.0,
            ScaleOffset = 0.0
        };

        AnalogInputPoints.Add(newPoint);
        SaveDataPoints();
    }

    private void AddBinaryOutputPoint()
    {
        var newIndex = BinaryOutputPoints.Count > 0 ?
            BinaryOutputPoints.Max(p => int.TryParse(p.Index, out var idx) ? idx : 0) + 1 : 0;
        
        var newPoint = new BinaryOutputPointType
        {
            Index = newIndex.ToString(),
            Name = $"BinaryOutput_{newIndex}",
            Description = "New binary output point",
            ChangeEventClass = EventClassType.One,
            CommandEventClass = EventClassType.One,
            IncludedInClass0Response = IncludedInClass0ResponseType.Always
        };

        BinaryOutputPoints.Add(newPoint);
        SaveDataPoints();
    }

    private void AddAnalogOutputPoint()
    {
        var newIndex = AnalogOutputPoints.Count > 0 ?
            AnalogOutputPoints.Max(p => int.TryParse(p.Index, out var idx) ? idx : 0) + 1 : 0;
        
        var newPoint = new AnalogOutputPointType
        {
            Index = newIndex.ToString(),
            Name = $"AnalogOutput_{newIndex}",
            Description = "New analog output point",
            ChangeEventClass = EventClassType.One,
            CommandEventClass = EventClassType.One,
            IncludedInClass0Response = IncludedInClass0ResponseType.Always,
            Units = "Units",
            ScaleFactor = 1.0,
            ScaleOffset = 0.0
        };

        AnalogOutputPoints.Add(newPoint);
        SaveDataPoints();
    }

    private void AddCounterPoint()
    {
        var newIndex = CounterPoints.Count > 0 ?
            CounterPoints.Max(p => int.TryParse(p.Index, out var idx) ? idx : 0) + 1 : 0;
        
        var newPoint = new CounterPointType
        {
            Index = newIndex.ToString(),
            Name = $"Counter_{newIndex}",
            Description = "New counter point",
            CounterEventClass = EventClassType.One,
            CountersIncludedInClass0 = IncludedInClass0ResponseType.Always,
            CounterRollOver = "65536"
        };

        CounterPoints.Add(newPoint);
        SaveDataPoints();
    }
}

public enum DataPointType
{
    BinaryInput,
    DoubleBitInput,
    BinaryOutput,
    AnalogInput,
    AnalogOutput,
    Counter,
    OctetString,
    VirtualTerminal
}
