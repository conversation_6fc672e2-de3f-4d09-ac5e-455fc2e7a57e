//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("onlineXmlFilesType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OnlineXmlFilesType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("dnpDPReadSupported")]
        public EmptyElement DnpDpReadSupported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("dnpDPCapReadSupported")]
        public EmptyElement DnpDpCapReadSupported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("dnpDPCfgReadSupported")]
        public EmptyElement DnpDpCfgReadSupported { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<XmlFileType> _xmlFile;
        
        [System.Xml.Serialization.XmlElementAttribute("xmlFile")]
        public System.Collections.ObjectModel.Collection<XmlFileType> XmlFile
        {
            get
            {
                return _xmlFile;
            }
            private set
            {
                _xmlFile = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the XmlFile collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool XmlFileSpecified
        {
            get
            {
                return (this.XmlFile.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="OnlineXmlFilesType" /> class.</para>
        /// </summary>
        public OnlineXmlFilesType()
        {
            this._xmlFile = new System.Collections.ObjectModel.Collection<XmlFileType>();
            this._note = new System.Collections.ObjectModel.Collection<string>();
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _note;
        
        [System.Xml.Serialization.XmlElementAttribute("note")]
        public System.Collections.ObjectModel.Collection<string> Note
        {
            get
            {
                return _note;
            }
            private set
            {
                _note = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Note collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NoteSpecified
        {
            get
            {
                return (this.Note.Count != 0);
            }
        }
    }
}
