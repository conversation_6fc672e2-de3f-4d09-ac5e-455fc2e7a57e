//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("controlStatusType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    public enum ControlStatusType
    {
        
        [System.Xml.Serialization.XmlEnumAttribute("SUCCESS")]
        Success,
        
        [System.Xml.Serialization.XmlEnumAttribute("TIMEOUT")]
        Timeout,
        
        [System.Xml.Serialization.XmlEnumAttribute("NO_SELECT")]
        NoSelect,
        
        [System.Xml.Serialization.XmlEnumAttribute("FORMAT_ERROR")]
        FormatError,
        
        [System.Xml.Serialization.XmlEnumAttribute("NOT_SUPPORTED")]
        NotSupported,
        
        [System.Xml.Serialization.XmlEnumAttribute("ALREADY_ACTIVE")]
        AlreadyActive,
        
        [System.Xml.Serialization.XmlEnumAttribute("HARDWARE_ERROR")]
        HardwareError,
        
        [System.Xml.Serialization.XmlEnumAttribute("LOCAL")]
        Local,
        
        [System.Xml.Serialization.XmlEnumAttribute("TOO_MANY_OBJS")]
        TooManyObjs,
        
        [System.Xml.Serialization.XmlEnumAttribute("NOT_AUTHORIZED")]
        NotAuthorized,
        
        [System.Xml.Serialization.XmlEnumAttribute("AUTOMATION_INHIBIT")]
        AutomationInhibit,
        
        [System.Xml.Serialization.XmlEnumAttribute("PROCESSING_LIMITED")]
        ProcessingLimited,
        
        [System.Xml.Serialization.XmlEnumAttribute("OUT_OF_RANGE")]
        OutOfRange,
    }
}
