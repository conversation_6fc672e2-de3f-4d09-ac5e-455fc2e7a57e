//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("counterPointType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CounterPointType : PointType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("defaultCounterStaticVariation")]
        public CounterStaticVariationPerPointType DefaultCounterStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultCounterEventVariation")]
        public CounterEventVariationPerPointType DefaultCounterEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("countersIncludedInClass0")]
        public IncludedInClass0ResponseType CountersIncludedInClass0 { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the CountersIncludedInClass0 property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CountersIncludedInClass0Specified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("counterEventClass")]
        public EventClassType CounterEventClass { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the CounterEventClass property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CounterEventClassSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenCounterExists")]
        public bool FrozenCounterExists { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the FrozenCounterExists property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FrozenCounterExistsSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultFrozenCounterStaticVariation")]
        public FrozenCounterStaticVariationPerPointType DefaultFrozenCounterStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultFrozenCounterEventVariation")]
        public FrozenCounterEventVariationPerPointType DefaultFrozenCounterEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenCountersIncludedInClass0")]
        public IncludedInClass0ResponseType FrozenCountersIncludedInClass0 { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the FrozenCountersIncludedInClass0 property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FrozenCountersIncludedInClass0Specified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenCounterEventClass")]
        public EventClassType FrozenCounterEventClass { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the FrozenCounterEventClass property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FrozenCounterEventClassSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("counterRollOver")]
        public string CounterRollOver { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("dnpData")]
        public CounterPointTypeDnpData DnpData { get; set; }
    }
}
