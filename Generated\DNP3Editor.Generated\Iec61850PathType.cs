//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("iec61850PathType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class Iec61850PathType
    {
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets the text value.</para>
        /// <para xml:lang="en">Pattern: (\p{L}[\p{L}\d]*){0,1}/(\p{L}[\p{L}\d]*(\(\d+\)){0,1}.){2,}\p{L}[\p{L}\d]*.</para>
        /// </summary>
        [System.ComponentModel.DataAnnotations.RegularExpressionAttribute("(\\p{L}[\\p{L}\\d]*){0,1}/(\\p{L}[\\p{L}\\d]*(\\(\\d+\\)){0,1}.){2,}\\p{L}[\\p{L}\\d]*")]
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value { get; set; }
        
        [System.Xml.Serialization.XmlAttributeAttribute("name")]
        public string Name { get; set; }
        
        [System.Xml.Serialization.XmlAttributeAttribute("fc")]
        public FcEnum Fc { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the Fc property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FcSpecified { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Pattern: \p{Lu}[\p{L},\d]*.</para>
        /// </summary>
        [System.ComponentModel.DataAnnotations.RegularExpressionAttribute("\\p{Lu}[\\p{L},\\d]*")]
        [System.Xml.Serialization.XmlAttributeAttribute("dataType")]
        public string DataType { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Minimum length: 1.</para>
        /// <para xml:lang="en">Pattern: \p{Lu}+.</para>
        /// </summary>
        [System.ComponentModel.DataAnnotations.MinLengthAttribute(1)]
        [System.ComponentModel.DataAnnotations.RegularExpressionAttribute("\\p{Lu}+")]
        [System.Xml.Serialization.XmlAttributeAttribute("cdc")]
        public string Cdc { get; set; }
        
        [System.Xml.Serialization.XmlAttributeAttribute("enumTypeId")]
        public string EnumTypeId { get; set; }
    }
}
