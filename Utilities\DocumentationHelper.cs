using System.Diagnostics;
using System.IO;

namespace DNP3Editor.Utilities;

public static class DocumentationHelper
{
    public static void OpenUserGuide()
    {
        var userGuidePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documentation", "UserGuide.pdf");
        
        if (File.Exists(userGuidePath))
        {
            Process.Start(new ProcessStartInfo(userGuidePath) { UseShellExecute = true });
        }
        else
        {
            // Open online documentation
            Process.Start(new ProcessStartInfo("https://your-company.com/dnp3editor/docs") { UseShellExecute = true });
        }
    }
}
