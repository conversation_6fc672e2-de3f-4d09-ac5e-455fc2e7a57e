//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("fcEnum", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    public enum FcEnum
    {
        
        [System.Xml.Serialization.XmlEnumAttribute("ST")]
        St,
        
        [System.Xml.Serialization.XmlEnumAttribute("MX")]
        Mx,
        
        [System.Xml.Serialization.XmlEnumAttribute("CO")]
        Co,
        
        [System.Xml.Serialization.XmlEnumAttribute("SP")]
        Sp,
        
        [System.Xml.Serialization.XmlEnumAttribute("SG")]
        Sg,
        
        [System.Xml.Serialization.XmlEnumAttribute("SE")]
        Se,
        
        [System.Xml.Serialization.XmlEnumAttribute("SV")]
        Sv,
        
        [System.Xml.Serialization.XmlEnumAttribute("CF")]
        Cf,
        
        [System.Xml.Serialization.XmlEnumAttribute("DC")]
        Dc,
        
        [System.Xml.Serialization.XmlEnumAttribute("EX")]
        Ex,
    }
}
