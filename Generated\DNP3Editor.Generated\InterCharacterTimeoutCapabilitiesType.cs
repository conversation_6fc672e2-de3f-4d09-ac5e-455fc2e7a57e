//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("interCharacterTimeoutCapabilitiesType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class InterCharacterTimeoutCapabilitiesType : CapabilitiesBaseType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("notChecked")]
        public EmptyElement NotChecked { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("noGapPermitted")]
        public EmptyElement NoGapPermitted { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("fixedBitTimes")]
        public string FixedBitTimes { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("rangeBitTimes")]
        public NonNegativeIntRangeType RangeBitTimes { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _selectableBitTimes;
        
        [System.Xml.Serialization.XmlElementAttribute("selectableBitTimes")]
        public System.Collections.ObjectModel.Collection<string> SelectableBitTimes
        {
            get
            {
                return _selectableBitTimes;
            }
            private set
            {
                _selectableBitTimes = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the SelectableBitTimes collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SelectableBitTimesSpecified
        {
            get
            {
                return (this.SelectableBitTimes.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="InterCharacterTimeoutCapabilitiesType" /> class.</para>
        /// </summary>
        public InterCharacterTimeoutCapabilitiesType()
        {
            this._selectableBitTimes = new System.Collections.ObjectModel.Collection<string>();
            this._selectableMilliseconds = new System.Collections.ObjectModel.Collection<string>();
            this._configurableOther = new System.Collections.ObjectModel.Collection<ConfigurableCustomType>();
            this._variable = new System.Collections.ObjectModel.Collection<CustomType>();
        }
        
        [System.Xml.Serialization.XmlElementAttribute("fixedMilliseconds")]
        public string FixedMilliseconds { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("rangeMilliseconds")]
        public NonNegativeIntRangeType RangeMilliseconds { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _selectableMilliseconds;
        
        [System.Xml.Serialization.XmlElementAttribute("selectableMilliseconds")]
        public System.Collections.ObjectModel.Collection<string> SelectableMilliseconds
        {
            get
            {
                return _selectableMilliseconds;
            }
            private set
            {
                _selectableMilliseconds = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the SelectableMilliseconds collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SelectableMillisecondsSpecified
        {
            get
            {
                return (this.SelectableMilliseconds.Count != 0);
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<ConfigurableCustomType> _configurableOther;
        
        [System.Xml.Serialization.XmlElementAttribute("configurableOther")]
        public System.Collections.ObjectModel.Collection<ConfigurableCustomType> ConfigurableOther
        {
            get
            {
                return _configurableOther;
            }
            private set
            {
                _configurableOther = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the ConfigurableOther collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ConfigurableOtherSpecified
        {
            get
            {
                return (this.ConfigurableOther.Count != 0);
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<CustomType> _variable;
        
        [System.Xml.Serialization.XmlElementAttribute("variable")]
        public System.Collections.ObjectModel.Collection<CustomType> Variable
        {
            get
            {
                return _variable;
            }
            private set
            {
                _variable = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Variable collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool VariableSpecified
        {
            get
            {
                return (this.Variable.Count != 0);
            }
        }
    }
}
