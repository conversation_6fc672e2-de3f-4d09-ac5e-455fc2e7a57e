//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("broadcastConfigType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class BroadcastConfigType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalitySupported")]
        public BroadcastFunctionalitySupportedType BroadcastFunctionalitySupported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC2Supported")]
        public BroadcastFunctionalityFc2Type BroadcastFunctionalityFc2Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC5Supported")]
        public BroadcastFunctionalityFc5Type BroadcastFunctionalityFc5Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC6Supported")]
        public BroadcastFunctionalityFc6Type BroadcastFunctionalityFc6Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC7Supported")]
        public BroadcastFunctionalityFc7Type BroadcastFunctionalityFc7Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC8Supported")]
        public BroadcastFunctionalityFc8Type BroadcastFunctionalityFc8Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC9Supported")]
        public BroadcastFunctionalityFc9Type BroadcastFunctionalityFc9Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC10Supported")]
        public BroadcastFunctionalityFc10Type BroadcastFunctionalityFc10Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC11Supported")]
        public BroadcastFunctionalityFc11Type BroadcastFunctionalityFc11Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC12Supported")]
        public BroadcastFunctionalityFc12Type BroadcastFunctionalityFc12Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC13Supported")]
        public BroadcastFunctionalityFc13Type BroadcastFunctionalityFc13Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC14Supported")]
        public BroadcastFunctionalityFc14Type BroadcastFunctionalityFc14Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC15Supported")]
        public BroadcastFunctionalityFc15Type BroadcastFunctionalityFc15Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC16Supported")]
        public BroadcastFunctionalityFc16Type BroadcastFunctionalityFc16Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC17Supported")]
        public BroadcastFunctionalityFc17Type BroadcastFunctionalityFc17Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC18Supported")]
        public BroadcastFunctionalityFc18Type BroadcastFunctionalityFc18Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC19Supported")]
        public BroadcastFunctionalityFc19Type BroadcastFunctionalityFc19Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC20Supported")]
        public BroadcastFunctionalityFc20Type BroadcastFunctionalityFc20Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC21Supported")]
        public BroadcastFunctionalityFc21Type BroadcastFunctionalityFc21Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC22Supported")]
        public BroadcastFunctionalityFc22Type BroadcastFunctionalityFc22Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC24Supported")]
        public BroadcastFunctionalityFc24Type BroadcastFunctionalityFc24Supported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("broadcastFunctionalityFC31Supported")]
        public BroadcastFunctionalityFc31Type BroadcastFunctionalityFc31Supported { get; set; }
    }
}
