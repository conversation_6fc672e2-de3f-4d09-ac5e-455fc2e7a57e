//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("outstationPerformanceType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OutstationPerformanceType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("maxTimeBaseDrift")]
        public TimingRangeType MaxTimeBaseDrift { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("outstationSetsIIN14")]
        public OutstationSetsIin14Type OutstationSetsIin14 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("referenceErrorViaDNP")]
        public TimingRangeType ReferenceErrorViaDnp { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("delayMeasurementError")]
        public TimingPerformanceType DelayMeasurementError { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("responseTime")]
        public TimingPerformanceType ResponseTime { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("startupToIIN14")]
        public TimingPerformanceType StartupToIin14 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("binaryOrDoubleBitEventError")]
        public TimingPerformanceType BinaryOrDoubleBitEventError { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("nonBinaryOrDoubleBitEventError")]
        public TimingPerformanceType NonBinaryOrDoubleBitEventError { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="OutstationPerformanceType" /> class.</para>
        /// </summary>
        public OutstationPerformanceType()
        {
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
    }
}
