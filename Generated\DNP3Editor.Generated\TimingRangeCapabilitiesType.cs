//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("timingRangeCapabilitiesType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TimingRangeCapabilitiesType : CapabilitiesBaseType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("fixed")]
        public double Fixed { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the Fixed property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FixedSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("range")]
        public DoubleRangeType Range { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<double> _selectable;
        
        [System.Xml.Serialization.XmlElementAttribute("selectable")]
        public System.Collections.ObjectModel.Collection<double> Selectable
        {
            get
            {
                return _selectable;
            }
            private set
            {
                _selectable = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Selectable collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SelectableSpecified
        {
            get
            {
                return (this.Selectable.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="TimingRangeCapabilitiesType" /> class.</para>
        /// </summary>
        public TimingRangeCapabilitiesType()
        {
            this._selectable = new System.Collections.ObjectModel.Collection<double>();
            this._other = new System.Collections.ObjectModel.Collection<NotConfigurableCustomType>();
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<NotConfigurableCustomType> _other;
        
        [System.Xml.Serialization.XmlElementAttribute("other")]
        public System.Collections.ObjectModel.Collection<NotConfigurableCustomType> Other
        {
            get
            {
                return _other;
            }
            private set
            {
                _other = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Other collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OtherSpecified
        {
            get
            {
                return (this.Other.Count != 0);
            }
        }
    }
}
