//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("DNP3DeviceProfileDocument", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlRootAttribute("DNP3DeviceProfileDocument", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    public partial class Dnp3DeviceProfileDocument
    {
        
        [System.Xml.Serialization.XmlElementAttribute("documentHeader")]
        public DocumentHeaderType DocumentHeader { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("referenceDevice")]
        public Dnp3DeviceOptionalType ReferenceDevice { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<Dnp3DeviceOptionalType> _auxiliaryInfo;
        
        [System.Xml.Serialization.XmlElementAttribute("auxiliaryInfo")]
        public System.Collections.ObjectModel.Collection<Dnp3DeviceOptionalType> AuxiliaryInfo
        {
            get
            {
                return _auxiliaryInfo;
            }
            private set
            {
                _auxiliaryInfo = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the AuxiliaryInfo collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AuxiliaryInfoSpecified
        {
            get
            {
                return (this.AuxiliaryInfo.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="Dnp3DeviceProfileDocument" /> class.</para>
        /// </summary>
        public Dnp3DeviceProfileDocument()
        {
            this._auxiliaryInfo = new System.Collections.ObjectModel.Collection<Dnp3DeviceOptionalType>();
        }
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlAttributeAttribute("schemaVersion")]
        public string SchemaVersion { get; set; }
    }
}
