//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("implementationTableType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ImplementationTableType
    {
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<SupportedVariationType> _supportedVariation;
        
        [System.Xml.Serialization.XmlElementAttribute("supportedVariation")]
        public System.Collections.ObjectModel.Collection<SupportedVariationType> SupportedVariation
        {
            get
            {
                return _supportedVariation;
            }
            private set
            {
                _supportedVariation = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the SupportedVariation collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SupportedVariationSpecified
        {
            get
            {
                return (this.SupportedVariation.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="ImplementationTableType" /> class.</para>
        /// </summary>
        public ImplementationTableType()
        {
            this._supportedVariation = new System.Collections.ObjectModel.Collection<SupportedVariationType>();
            this._supportedFunctionCode = new System.Collections.ObjectModel.Collection<SupportedFunctionCodeType>();
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<SupportedFunctionCodeType> _supportedFunctionCode;
        
        [System.Xml.Serialization.XmlElementAttribute("supportedFunctionCode")]
        public System.Collections.ObjectModel.Collection<SupportedFunctionCodeType> SupportedFunctionCode
        {
            get
            {
                return _supportedFunctionCode;
            }
            private set
            {
                _supportedFunctionCode = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the SupportedFunctionCode collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SupportedFunctionCodeSpecified
        {
            get
            {
                return (this.SupportedFunctionCode.Count != 0);
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
    }
}
