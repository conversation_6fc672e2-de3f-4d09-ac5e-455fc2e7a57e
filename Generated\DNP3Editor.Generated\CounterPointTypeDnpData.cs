//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("CounterPointTypeDnpData", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CounterPointTypeDnpData
    {
        
        [System.Xml.Serialization.XmlElementAttribute("runningCounterValue")]
        public uint RunningCounterValue { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the RunningCounterValue property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RunningCounterValueSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("runningCounterQuality")]
        public byte RunningCounterQuality { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the RunningCounterQuality property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RunningCounterQualitySpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("runningCounterTimestamp", DataType="dateTime")]
        public System.DateTime RunningCounterTimestamp { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the RunningCounterTimestamp property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RunningCounterTimestampSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenCounterValue")]
        public uint FrozenCounterValue { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the FrozenCounterValue property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FrozenCounterValueSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenCounterQuality")]
        public byte FrozenCounterQuality { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the FrozenCounterQuality property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FrozenCounterQualitySpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenCounterTimestamp", DataType="dateTime")]
        public System.DateTime FrozenCounterTimestamp { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the FrozenCounterTimestamp property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FrozenCounterTimestampSpecified { get; set; }
    }
}
