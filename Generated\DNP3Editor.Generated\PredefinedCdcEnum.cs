//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("predefinedCDCEnum", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    public enum PredefinedCdcEnum
    {
        
        [System.Xml.Serialization.XmlEnumAttribute("SPS")]
        Sps,
        
        [System.Xml.Serialization.XmlEnumAttribute("DPS")]
        Dps,
        
        [System.Xml.Serialization.XmlEnumAttribute("INS")]
        Ins,
        
        [System.Xml.Serialization.XmlEnumAttribute("ACT")]
        Act,
        
        [System.Xml.Serialization.XmlEnumAttribute("ACD")]
        Acd,
        
        [System.Xml.Serialization.XmlEnumAttribute("SEC")]
        Sec,
        
        [System.Xml.Serialization.XmlEnumAttribute("BCR")]
        Bcr,
        
        [System.Xml.Serialization.XmlEnumAttribute("MV")]
        Mv,
        
        [System.Xml.Serialization.XmlEnumAttribute("CMV")]
        Cmv,
        
        [System.Xml.Serialization.XmlEnumAttribute("SAV")]
        Sav,
        
        [System.Xml.Serialization.XmlEnumAttribute("WYE")]
        Wye,
        
        [System.Xml.Serialization.XmlEnumAttribute("DEL")]
        Del,
        
        [System.Xml.Serialization.XmlEnumAttribute("SEQ")]
        Seq,
        
        [System.Xml.Serialization.XmlEnumAttribute("HMV")]
        Hmv,
        
        [System.Xml.Serialization.XmlEnumAttribute("HWYE")]
        Hwye,
        
        [System.Xml.Serialization.XmlEnumAttribute("HDEL")]
        Hdel,
        
        [System.Xml.Serialization.XmlEnumAttribute("SPC")]
        Spc,
        
        [System.Xml.Serialization.XmlEnumAttribute("DPC")]
        Dpc,
        
        [System.Xml.Serialization.XmlEnumAttribute("INC")]
        Inc,
        
        [System.Xml.Serialization.XmlEnumAttribute("BSC")]
        Bsc,
        
        [System.Xml.Serialization.XmlEnumAttribute("ISC")]
        Isc,
        
        [System.Xml.Serialization.XmlEnumAttribute("APC")]
        Apc,
        
        [System.Xml.Serialization.XmlEnumAttribute("SPG")]
        Spg,
        
        [System.Xml.Serialization.XmlEnumAttribute("ING")]
        Ing,
        
        [System.Xml.Serialization.XmlEnumAttribute("ASG")]
        Asg,
        
        [System.Xml.Serialization.XmlEnumAttribute("CURVE")]
        Curve,
        
        [System.Xml.Serialization.XmlEnumAttribute("DPL")]
        Dpl,
        
        [System.Xml.Serialization.XmlEnumAttribute("LPL")]
        Lpl,
        
        [System.Xml.Serialization.XmlEnumAttribute("CSD")]
        Csd,
    }
}
