using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Reflection;

namespace DNP3Editor.ViewModels;

public partial class AboutViewModel : ObservableObject
{
    [ObservableProperty]
    private string applicationName = "DNP3 Device Profile Editor";

    [ObservableProperty]
    private string version;

    [ObservableProperty]
    private string copyright = "© 2024 DNP3 Editor";

    [ObservableProperty]
    private string description = "A comprehensive editor for DNP3 device profiles";

    [ObservableProperty]
    private string buildDate;

    public AboutViewModel()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var assemblyName = assembly.GetName();
        
        Version = assemblyName.Version?.ToString() ?? "*******";
        
        var buildDateAttribute = assembly.GetCustomAttribute<AssemblyMetadataAttribute>();
        BuildDate = buildDateAttribute?.Value ?? DateTime.Now.ToString("yyyy-MM-dd");
    }

    [RelayCommand]
    private void OpenWebsite()
    {
        // TODO: Open project website
    }

    [RelayCommand]
    private void ShowLicense()
    {
        // TODO: Show license information
    }

    [RelayCommand]
    private void CheckForUpdates()
    {
        // TODO: Check for application updates
    }
}
