//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("byAsymmetricCryptographyCurrentValueType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ByAsymmetricCryptographyCurrentValueType : CurrentValueBaseType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("RSAES-OAEP-1024WithSHA-1-HMAC")]
        public EmptyElement RsaesOaep1024WithSha1Hmac { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("RSAES-OAEP-2048WithSHA-256-HMAC")]
        public EmptyElement RsaesOaep2048WithSha256Hmac { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("RSAES-OAEP-3072WithSHA-256-HMAC")]
        public EmptyElement RsaesOaep3072WithSha256Hmac { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("RSAES-OAEP-2048WithAES-GMAC")]
        public EmptyElement RsaesOaep2048WithAesGmac { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("RSAES-OAEP-3072WithAES-GMAC")]
        public EmptyElement RsaesOaep3072WithAesGmac { get; set; }
    }
}
