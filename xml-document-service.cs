using System.Text;
using System.Xml;
using System.Xml.Schema;
using System.Xml.Serialization;
using Microsoft.Extensions.Logging;

using DNP3Editor.Models;
using DNP3Editor.Generated;

namespace DNP3Editor.Services;

public interface IXmlDocumentService
{
    Task<Dnp3DeviceProfileDocument> LoadDocumentAsync(string filePath);
    Task SaveDocumentAsync(Dnp3DeviceProfileDocument document, string filePath);
    Task<ValidationResult> ValidateDocumentAsync(string filePath);
    Task<ValidationResult> ValidateDocumentAsync(Dnp3DeviceProfileDocument document);
}

public class XmlDocumentService : IXmlDocumentService
{
    private readonly ILogger<XmlDocumentService> _logger;
    private readonly XmlSchemaSet _schemaSet;
    private readonly XmlSerializer _serializer;

    public XmlDocumentService(ILogger<XmlDocumentService> logger)
    {
        _logger = logger;
        _serializer = new XmlSerializer(typeof(Dnp3DeviceProfileDocument));
        _schemaSet = LoadSchema();
    }

    public async Task<Dnp3DeviceProfileDocument> LoadDocumentAsync(string filePath)
    {
        try
        {
            _logger.LogInformation("Loading document from {FilePath}", filePath);

            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            using var reader = XmlReader.Create(fileStream, CreateReaderSettings());

            var document = (Dnp3DeviceProfileDocument?)_serializer.Deserialize(reader);
            
            if (document == null)
            {
                throw new InvalidOperationException("Failed to deserialize document");
            }

            _logger.LogInformation("Document loaded successfully from {FilePath}", filePath);
            return document;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading document from {FilePath}", filePath);
            throw;
        }
    }

    public async Task SaveDocumentAsync(Dnp3DeviceProfileDocument document, string filePath)
    {
        try
        {
            _logger.LogInformation("Saving document to {FilePath}", filePath);

            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = Encoding.UTF8,
                WriteEndDocumentOnClose = true
            };

            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
            using var writer = XmlWriter.Create(fileStream, settings);

            var namespaces = new XmlSerializerNamespaces();
            namespaces.Add("", "http://www.dnp3.org/DNP3/DeviceProfile/November2014");

            _serializer.Serialize(writer, document, namespaces);

            _logger.LogInformation("Document saved successfully to {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving document to {FilePath}", filePath);
            throw;
        }
    }

    public async Task<ValidationResult> ValidateDocumentAsync(string filePath)
    {
        try
        {
            var document = await LoadDocumentAsync(filePath);
            return await ValidateDocumentAsync(document);
        }
        catch (Exception ex)
        {
            return new ValidationResult
            {
                IsValid = false,
                Errors = new List<string> { $"Error loading document: {ex.Message}" }
            };
        }
    }

    public async Task<ValidationResult> ValidateDocumentAsync(Dnp3DeviceProfileDocument document)
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        try
        {
            // XML Schema validation
            using var memoryStream = new MemoryStream();
            using var writer = XmlWriter.Create(memoryStream);
            
            _serializer.Serialize(writer, document);
            
            memoryStream.Position = 0;
            using var reader = XmlReader.Create(memoryStream, CreateValidatingReaderSettings(errors));
            
            while (reader.Read()) { } // Process entire document

            // Business rule validation
            ValidateBusinessRules(document, errors, warnings);

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors,
                Warnings = warnings
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during document validation");
            errors.Add($"Validation error: {ex.Message}");
            
            return new ValidationResult
            {
                IsValid = false,
                Errors = errors,
                Warnings = warnings
            };
        }
    }

    private XmlSchemaSet LoadSchema()
    {
        try
        {
            var schemaSet = new XmlSchemaSet();
            var schemaPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DNP3DeviceProfileNovember2014.xsd");
            
            if (File.Exists(schemaPath))
            {
                schemaSet.Add("http://www.dnp3.org/DNP3/DeviceProfile/November2014", schemaPath);
                schemaSet.Compile();
                _logger.LogInformation("XSD schema loaded successfully");
            }
            else
            {
                _logger.LogWarning("XSD schema file not found at {SchemaPath}", schemaPath);
            }

            return schemaSet;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading XSD schema");
            return new XmlSchemaSet();
        }
    }

    private XmlReaderSettings CreateReaderSettings()
    {
        return new XmlReaderSettings
        {
            IgnoreWhitespace = true,
            IgnoreComments = true,
            ValidationType = ValidationType.None
        };
    }

    private XmlReaderSettings CreateValidatingReaderSettings(List<string> errors)
    {
        var settings = new XmlReaderSettings
        {
            ValidationType = ValidationType.Schema,
            Schemas = _schemaSet
        };

        settings.ValidationEventHandler += (sender, e) =>
        {
            if (e.Severity == XmlSeverityType.Error)
            {
                errors.Add($"Schema validation error at line {e.Exception?.LineNumber}: {e.Message}");
            }
        };

        return settings;
    }

    private void ValidateBusinessRules(Dnp3DeviceProfileDocument document, List<string> errors, List<string> warnings)
    {
        // DNP3-specific business rule validation
        
        if (document.ReferenceDevice?.Configuration != null)
        {
            var config = document.ReferenceDevice.Configuration;
            
            // Validate device function
            if (config.DeviceConfig?.DeviceFunction?.CurrentValue == null)
            {
                warnings.Add("Device function is not specified");
            }

            // Validate data link address ranges
            if (config.LinkConfig?.DataLinkAddress?.CurrentValue?.Value != null)
            {
                var address = config.LinkConfig.DataLinkAddress.CurrentValue.Value;
                if (address >= 65520)
                {
                    errors.Add($"Data link address {address} is reserved and cannot be used");
                }
            }

            // Validate baud rate
            if (config.SerialConfig?.BaudRate?.CurrentValue?.Value != null)
            {
                var baudRateString = config.SerialConfig.BaudRate.CurrentValue.Value;
                var validBaudRates = new[] { 300, 600, 1200, 2400, 4800, 9600, 19200, 38400, 57600, 115200 };

                if (int.TryParse(baudRateString, out var baudRate) && !validBaudRates.Contains(baudRate))
                {
                    warnings.Add($"Baud rate {baudRate} is not a standard DNP3 baud rate");
                }
            }

            // Validate event class assignments
            ValidateEventClassAssignments(document, errors, warnings);
        }

        // Validate data points
        if (document.ReferenceDevice?.DataPointsList != null)
        {
            ValidateDataPoints(document.ReferenceDevice.DataPointsList, errors, warnings);
        }
    }

    private void ValidateEventClassAssignments(Dnp3DeviceProfileDocument document, List<string> errors, List<string> warnings)
    {
        // Check for valid event class assignments (1, 2, or 3)
        var dataPoints = document.ReferenceDevice?.DataPointsList;
        
        if (dataPoints?.BinaryInputPoints?.DataPoints != null)
        {
            foreach (var point in dataPoints.BinaryInputPoints.DataPoints)
            {
                if (point.ChangeEventClass != null)
                {
                    var eventClass = point.ChangeEventClass.ToString();
                    if (eventClass != "One" && eventClass != "Two" && eventClass != "Three" && eventClass != "None")
                    {
                        errors.Add($"Binary input point {point.Index} has invalid event class: {eventClass}");
                    }
                }
            }
        }
    }

    private void ValidateDataPoints(DnpDataPointsListType dataPoints, List<string> errors, List<string> warnings)
    {
        // Validate binary input points
        if (dataPoints.BinaryInputPoints?.DataPoints != null)
        {
            var indices = new HashSet<string>();
            foreach (var point in dataPoints.BinaryInputPoints.DataPoints)
            {
                if (indices.Contains(point.Index))
                {
                    errors.Add($"Duplicate binary input point index: {point.Index}");
                }
                else
                {
                    indices.Add(point.Index);
                }

                if (string.IsNullOrWhiteSpace(point.Name))
                {
                    warnings.Add($"Binary input point {point.Index} has no name");
                }
            }
        }

        // Validate analog input points
        if (dataPoints.AnalogInputPoints?.DataPoints != null)
        {
            var indices = new HashSet<string>();
            foreach (var point in dataPoints.AnalogInputPoints.DataPoints)
            {
                if (indices.Contains(point.Index))
                {
                    errors.Add($"Duplicate analog input point index: {point.Index}");
                }
                else
                {
                    indices.Add(point.Index);
                }

                // Validate scaling parameters
                if (point.ScaleFactorSpecified && point.ScaleFactor == 0)
                {
                    errors.Add($"Analog input point {point.Index} has zero scale factor");
                }
            }
        }

        // Similar validation for other point types...
    }
}