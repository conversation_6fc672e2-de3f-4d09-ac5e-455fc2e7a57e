//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("controlStatusCodesSupportedCapabilitiesType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ControlStatusCodesSupportedCapabilitiesType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("code1")]
        public EmptyElement Code1 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code2")]
        public EmptyElement Code2 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code3")]
        public EmptyElement Code3 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code4")]
        public EmptyElement Code4 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code5")]
        public EmptyElement Code5 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code6")]
        public EmptyElement Code6 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code7")]
        public EmptyElement Code7 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code8")]
        public EmptyElement Code8 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code9")]
        public EmptyElement Code9 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code10")]
        public EmptyElement Code10 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code11")]
        public EmptyElement Code11 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code12")]
        public EmptyElement Code12 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code13")]
        public EmptyElement Code13 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code14")]
        public EmptyElement Code14 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code15")]
        public EmptyElement Code15 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code16")]
        public EmptyElement Code16 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code17")]
        public EmptyElement Code17 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code18")]
        public EmptyElement Code18 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code126")]
        public EmptyElement Code126 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("code127")]
        public EmptyElement Code127 { get; set; }
    }
}
