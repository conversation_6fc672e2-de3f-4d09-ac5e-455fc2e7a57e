//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("iec61850RuleMappingType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class Iec61850RuleMappingType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("note")]
        public string Note { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Pattern: \p{Lu}[\p{Lu},_]*.</para>
        /// </summary>
        [System.ComponentModel.DataAnnotations.RegularExpressionAttribute("\\p{Lu}[\\p{Lu},_]*")]
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("rule")]
        public string Rule { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<Dnp3XPathType> _dnp3XPath;
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("dnp3XPath")]
        public System.Collections.ObjectModel.Collection<Dnp3XPathType> Dnp3XPath
        {
            get
            {
                return _dnp3XPath;
            }
            private set
            {
                _dnp3XPath = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="Iec61850RuleMappingType" /> class.</para>
        /// </summary>
        public Iec61850RuleMappingType()
        {
            this._dnp3XPath = new System.Collections.ObjectModel.Collection<Dnp3XPathType>();
        }
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("iec61850Path")]
        public Iec61850PathType Iec61850Path { get; set; }
    }
}
