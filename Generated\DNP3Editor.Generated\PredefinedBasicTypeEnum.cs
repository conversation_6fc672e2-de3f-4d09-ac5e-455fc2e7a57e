//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("predefinedBasicTypeEnum", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    public enum PredefinedBasicTypeEnum
    {
        
        [System.Xml.Serialization.XmlEnumAttribute("BOOLEAN")]
        Boolean,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT8")]
        Int8,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT16")]
        Int16,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT24")]
        Int24,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT32")]
        Int32,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT128")]
        Int128,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT8U")]
        Int8U,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT16U")]
        Int16U,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT24U")]
        Int24U,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT32U")]
        Int32U,
        
        [System.Xml.Serialization.XmlEnumAttribute("FLOAT32")]
        Float32,
        
        [System.Xml.Serialization.XmlEnumAttribute("FLOAT64")]
        Float64,
        
        Enum,
        
        Dbpos,
        
        Tcmd,
        
        Quality,
        
        Timestamp,
        
        VisString32,
        
        VisString64,
        
        VisString255,
        
        Octet64,
        
        Struct,
        
        EntryTime,
        
        Unicode255,
    }
}
