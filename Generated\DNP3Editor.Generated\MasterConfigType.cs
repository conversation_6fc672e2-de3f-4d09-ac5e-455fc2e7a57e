//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("masterConfigType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class MasterConfigType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("responseTimeout")]
        public ResponseTimeoutType ResponseTimeout { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("applicationLayerRetries")]
        public ApplicationLayerRetriesType ApplicationLayerRetries { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("responseIncrementalTimeout")]
        public ResponseIncrementalTimeoutType ResponseIncrementalTimeout { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("issuesControlsToOfflineDevices")]
        public IssuesControlsToOfflineDevicesType IssuesControlsToOfflineDevices { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("issuesControlsToOffscanDevices")]
        public IssuesControlsToOffscanDevicesType IssuesControlsToOffscanDevices { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxControlRetriesSameSN")]
        public MaxControlRetriesSameSnType MaxControlRetriesSameSn { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxControlRetriesNewSN")]
        public MaxControlRetriesNewSnType MaxControlRetriesNewSn { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxTimeErrorScheduledFreezes")]
        public MaxTimeErrorScheduledFreezesType MaxTimeErrorScheduledFreezes { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxTimeErrorRepetitiveFreezes")]
        public MaxTimeErrorRepetitiveFreezesType MaxTimeErrorRepetitiveFreezes { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("actionsAffectingFreezeRequests")]
        public ActionsAffectingFreezeRequestsType ActionsAffectingFreezeRequests { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("schedulingAlgorithm")]
        public SchedulingAlgorithmType SchedulingAlgorithm { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="MasterConfigType" /> class.</para>
        /// </summary>
        public MasterConfigType()
        {
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
    }
}
