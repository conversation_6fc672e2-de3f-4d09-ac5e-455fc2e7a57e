using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace DNP3Editor.ViewModels;

public partial class LicenseViewModel : ObservableObject
{
    [ObservableProperty]
    private string licenseKey = "";

    [ObservableProperty]
    private string licensedTo = "";

    [ObservableProperty]
    private DateTime? expirationDate;

    [ObservableProperty]
    private bool isLicenseValid;

    [ObservableProperty]
    private string licenseStatus = "No License";

    [ObservableProperty]
    private string licenseText = "";

    public LicenseViewModel()
    {
        CheckLicenseStatus();
    }

    [RelayCommand]
    private void ValidateLicense()
    {
        // TODO: Implement license validation
        if (string.IsNullOrWhiteSpace(LicenseKey))
        {
            LicenseStatus = "Invalid License Key";
            IsLicenseValid = false;
            return;
        }

        // Placeholder validation
        IsLicenseValid = true;
        LicenseStatus = "Valid License";
        LicensedTo = "Licensed User";
        ExpirationDate = DateTime.Now.AddYears(1);
    }

    [RelayCommand]
    private void ClearLicense()
    {
        LicenseKey = "";
        LicensedTo = "";
        ExpirationDate = null;
        IsLicenseValid = false;
        LicenseStatus = "No License";
    }

    [RelayCommand]
    private void ImportLicense()
    {
        // TODO: Import license from file
    }

    [RelayCommand]
    private void RequestTrial()
    {
        // TODO: Request trial license
    }

    private void CheckLicenseStatus()
    {
        // TODO: Check current license status
        IsLicenseValid = false;
        LicenseStatus = "No License";
    }
}
