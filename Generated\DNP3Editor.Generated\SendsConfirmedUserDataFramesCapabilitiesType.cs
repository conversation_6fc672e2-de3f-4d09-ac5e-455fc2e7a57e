//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("sendsConfirmedUserDataFramesCapabilitiesType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SendsConfirmedUserDataFramesCapabilitiesType : CapabilitiesBaseType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("never")]
        public EmptyElement Never { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("always")]
        public EmptyElement Always { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<CustomType> _sometimes;
        
        [System.Xml.Serialization.XmlElementAttribute("sometimes")]
        public System.Collections.ObjectModel.Collection<CustomType> Sometimes
        {
            get
            {
                return _sometimes;
            }
            private set
            {
                _sometimes = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Sometimes collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SometimesSpecified
        {
            get
            {
                return (this.Sometimes.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="SendsConfirmedUserDataFramesCapabilitiesType" /> class.</para>
        /// </summary>
        public SendsConfirmedUserDataFramesCapabilitiesType()
        {
            this._sometimes = new System.Collections.ObjectModel.Collection<CustomType>();
        }
    }
}
