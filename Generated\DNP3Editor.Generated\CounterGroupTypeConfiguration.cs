//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("CounterGroupTypeConfiguration", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CounterGroupTypeConfiguration
    {
        
        [System.Xml.Serialization.XmlElementAttribute("defaultCounterStaticVariation")]
        public DefaultCounterStaticVariationType DefaultCounterStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultCounterEventVariation")]
        public DefaultCounterEventVariationType DefaultCounterEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("counterClass0ResponseMode")]
        public Class0ResponseModeType CounterClass0ResponseMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("counterEventReportingMode")]
        public AnalogEventReportingModeType CounterEventReportingMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultFrozenCounterStaticVariation")]
        public DefaultFrozenCounterStaticVariationType DefaultFrozenCounterStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultFrozenCounterEventVariation")]
        public DefaultFrozenCounterEventVariationType DefaultFrozenCounterEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenCounterClass0ResponseMode")]
        public Class0ResponseModeType FrozenCounterClass0ResponseMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenCounterEventReportingMode")]
        public FrozenCounterEventReportingModeType FrozenCounterEventReportingMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("counterRollOver")]
        public CounterRollOverType CounterRollOver { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("countersFrozen")]
        public CountersFrozenType CountersFrozen { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("eventBufferPerObjectGroup")]
        public EventBufferPerObjectGroupType EventBufferPerObjectGroup { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenEventBufferPerObjectGroup")]
        public EventBufferPerObjectGroupType FrozenEventBufferPerObjectGroup { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("reportValueChangeCounterEvents")]
        public ReportValueChangeCounterEventsType ReportValueChangeCounterEvents { get; set; }
    }
}
