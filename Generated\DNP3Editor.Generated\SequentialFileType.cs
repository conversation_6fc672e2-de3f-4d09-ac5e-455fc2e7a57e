//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("sequentialFileType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SequentialFileType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("fileName")]
        public string FileName { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("eventClass")]
        public EventClassType EventClass { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the EventClass property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool EventClassSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("readAuthenticateRequired")]
        public bool ReadAuthenticateRequired { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the ReadAuthenticateRequired property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ReadAuthenticateRequiredSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("writeAuthenticateRequired")]
        public bool WriteAuthenticateRequired { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the WriteAuthenticateRequired property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool WriteAuthenticateRequiredSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("deleteAuthenticateRequired")]
        public bool DeleteAuthenticateRequired { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the DeleteAuthenticateRequired property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DeleteAuthenticateRequiredSpecified { get; set; }
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("description")]
        public string Description { get; set; }
    }
}
