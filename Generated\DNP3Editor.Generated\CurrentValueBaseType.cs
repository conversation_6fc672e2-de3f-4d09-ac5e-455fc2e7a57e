//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("currentValueBaseType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ActionsAffectingFreezeRequestsCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AdditionalCriticalFCsCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogDeadbandAlgorithmCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogDeadbandAlgorithmPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogDeadbandAssignmentsCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogDeadbandAssignmentsPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogEventReportingModeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputEventReportingModePerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputEventVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputEventVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputStaticVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputStaticVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogOutputEventVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogOutputEventVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogOutputStaticVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogOutputStaticVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ApplicationLayerConfirmTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ApplicationLayerRetriesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AssertedCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BaudRateCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryInputEventVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryInputEventVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryInputStaticVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryInputStaticVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryOutputEventVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryOutputEventVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryOutputStaticVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryOutputStaticVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc10CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc11CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc12CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc13CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc14CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc15CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc16CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc17CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc18CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc19CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc22CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc24CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc31CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc5CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc6CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc7CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc8CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc9CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalitySupportedCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ByAnyOtherCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ByAsymmetricCryptographyCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ByEventClassCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BySymmetricCryptographyCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CertificateRevocationCheckTimeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ChangeCipherRequestTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Class0ResponseModeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ClearRestartCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ConfigurationMethodsCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ConfigurationSignatureSupportedCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ConnectionsSupportedCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterEventVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterEventVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterRollOverCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CountersFrozenCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterStaticVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterStaticVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DataLinkAddressCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DestinationUdpPortCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DeviceFunctionCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DeviceNameCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DeviceTroubleBitCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DnpLevelCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DocumentVersionCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DoubleBitInputEventVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DoubleBitInputEventVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DoubleBitInputStaticVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DoubleBitInputStaticVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EventBufferOrganizationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EventBufferOverflowBehaviorCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EventBufferPerObjectGroupCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EventReportingModeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ExpectedSourceAddressCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FileHandleTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FlowControlCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FragmentTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenAnalogEventReportingModeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenAnalogInputEventVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenAnalogInputStaticVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenCounterEventReportingModeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenCounterEventVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenCounterEventVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenCounterStaticVariationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenCounterStaticVariationPerPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HardwareVersionCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HoldTimeAfterEventCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InterCharacterGapCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InterCharacterTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IpAddressCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IpAddressesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IpPortNameCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IssuesControlsToOfflineDevicesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IssuesControlsToOffscanDevicesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(KeyWrapAlgorithmCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LinkLayerConfirmTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LinkStatusIntervalCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LocalUdpPortCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MacAlgorithmRequestedCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxControlRetriesNewSnCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxControlRetriesSameSnCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxDataLinkRetriesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxErrorCountCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxFragmentSizeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxFrameSizeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxObjectsControlRequestCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxOpenFilesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxTimeBetweenSelectAndOperateCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxTimeErrorRepetitiveFreezesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxTimeErrorScheduledFreezesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxUnsolicitedRetriesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxUsersCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MultipleMasterConnectionsCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MultipleOutstationConnectionsCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(NumberCaSupportedCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(NumberOfClassEventsCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ObjectGroupSelectionCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OtherTriggerConditionsCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OutputCommandEventObjectsCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OutstationSetsIin14CurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PortNameCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PulseTimeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RemoteUpdateKeyChangeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ReportValueChangeCounterEventsTypeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RequestsApplicationConfirmationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RequestsLastFragmentConfirmationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResponseIncrementalTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResponseTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RetriggerHoldTimerCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SecureAuthenticationSupportedCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SecurityResponseTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SelfAddressSupportCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SendsConfirmedUserDataFramesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SerialParametersCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionKeyChangeIntervalCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionKeyChangeMessageCountCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SettingsPreservedThroughDeviceRestartCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SoftwareVersionCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SometimesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SourceAddressValidationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SubnetMaskCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SupportedFunctionBlocksCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SupportsCollisionAvoidanceCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SupportsMixedObjectGroupsInControlCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SupportsUnsolicitedReportingCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TcpConnectionEstablishmentCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TcpKeepAliveTimerCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TcpListenPortCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TcpPortOfRemoteDeviceCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TimeSynchronizationCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TimeSyncRequiredCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TimingPerformanceCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TimingRangeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TlsCipherSuitesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TypeOfEndPointCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UdpPortForResponsesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UdpPortForUnsolicitedNullResponsesCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UnsolicitedResponseConfirmationTimeoutCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(VendorNameCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(WriteAnyOtherCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(WriteClockCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(WriteLastRecordedTimeCurrentValueType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(YesNoCurrentValueType))]
    public partial class CurrentValueBaseType
    {
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _note;
        
        [System.Xml.Serialization.XmlElementAttribute("note")]
        public System.Collections.ObjectModel.Collection<string> Note
        {
            get
            {
                return _note;
            }
            private set
            {
                _note = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Note collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NoteSpecified
        {
            get
            {
                return (this.Note.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="CurrentValueBaseType" /> class.</para>
        /// </summary>
        public CurrentValueBaseType()
        {
            this._note = new System.Collections.ObjectModel.Collection<string>();
        }
    }
}
