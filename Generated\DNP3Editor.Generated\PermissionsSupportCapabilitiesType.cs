//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("permissionsSupportCapabilitiesType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PermissionsSupportCapabilitiesType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("ownerReadAllowed")]
        public EmptyElement OwnerReadAllowed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("ownerWriteAllowed")]
        public EmptyElement OwnerWriteAllowed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("ownerExecuteAllowed")]
        public EmptyElement OwnerExecuteAllowed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("groupReadAllowed")]
        public EmptyElement GroupReadAllowed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("groupWriteAllowed")]
        public EmptyElement GroupWriteAllowed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("groupExecuteAllowed")]
        public EmptyElement GroupExecuteAllowed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("worldReadAllowed")]
        public EmptyElement WorldReadAllowed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("worldWriteAllowed")]
        public EmptyElement WorldWriteAllowed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("worldExecuteAllowed")]
        public EmptyElement WorldExecuteAllowed { get; set; }
    }
}
