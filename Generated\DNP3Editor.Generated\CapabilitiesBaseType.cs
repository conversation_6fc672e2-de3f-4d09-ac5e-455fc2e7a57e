//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("capabilitiesBaseType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ActionsAffectingFreezeRequestsCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AdditionalCriticalFCsCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AlgorithmNameType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogDeadbandAlgorithmCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogDeadbandAssignmentsCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogEventReportingModeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputEventVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputStaticVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogOutputEventVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogOutputStaticVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ApplicationLayerConfirmTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ApplicationLayerRetriesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AssertedCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BaudRateCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryInputEventVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryInputStaticVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryOutputEventVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryOutputStaticVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc10CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc11CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc12CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc13CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc14CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc15CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc16CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc17CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc18CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc19CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc22CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc24CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc31CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc5CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc6CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc7CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc8CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalityFc9CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BroadcastFunctionalitySupportedCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ByAnyOtherCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ByAsymmetricCryptographyCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ByEventClassCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BySymmetricCryptographyCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CertificateRevocationCheckTimeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ChangeCipherRequestTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Class0ResponseModeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ClearRestartCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ConfigurationMethodsCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ConfigurationSignatureSupportedCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ConnectionsSupportedCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterEventVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterRollOverCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CountersFrozenCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterStaticVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DataLinkAddressCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DatasetClass0ResponseModeType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DatasetPropertiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DefinitionOfDataSetType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DestinationUdpPortCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DeviceFunctionCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DeviceTroubleBitCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DnpLevelCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DoubleBitInputEventVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DoubleBitInputStaticVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EventAssignedClassType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EventBufferOrganizationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EventBufferOverflowBehaviorCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EventBufferPerObjectGroupCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(EventReportingModeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ExpectedSourceAddressCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FileHandleTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FlowControlCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FragmentTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenAnalogEventReportingModeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenAnalogInputEventVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenAnalogInputStaticVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenCounterEventReportingModeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenCounterEventVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FrozenCounterStaticVariationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(HoldTimeAfterEventCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InterCharacterGapCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InterCharacterTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IssuesControlsToOfflineDevicesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IssuesControlsToOffscanDevicesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(KeyWrapAlgorithmCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LinkLayerConfirmTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LinkStatusIntervalCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LocalUdpPortCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MacAlgorithmRequestedCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxControlRetriesNewSnCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxControlRetriesSameSnCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxDataLinkRetriesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxErrorCountCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxFragmentSizeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxFrameSizeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxObjectsControlRequestCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxOpenFilesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxTimeBetweenSelectAndOperateCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxUnsolicitedRetriesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MaxUsersCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MultipleMasterConnectionsCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(MultipleOutstationConnectionsCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(NumberOfClassEventsCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ObjectGroupSelectionCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OtherTriggerConditionsCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OutputCommandEventObjectsCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OutstationSetsIin14CapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PointListDefinitionType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PulseTimeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RemoteUpdateKeyChangeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ReportValueChangeCounterEventsTypeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RequestsApplicationConfirmationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RequestsApplicationConfirmationRadioButtonType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RequestsLastFragmentConfirmationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResponseIncrementalTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResponseTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(RetriggerHoldTimerCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SecureAuthenticationSupportedCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SecurityResponseTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SelfAddressSupportCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SendsConfirmedUserDataFramesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SerialParametersCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionKeyChangeIntervalCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionKeyChangeMessageCountCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SettingsPreservedThroughDeviceRestartCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SometimesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SourceAddressValidationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SupportedFunctionBlocksCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SupportsCollisionAvoidanceCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SupportsMixedObjectGroupsInControlCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SupportsUnsolicitedReportingCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TcpConnectionEstablishmentCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TcpKeepAliveTimerCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TcpListenPortCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TcpPortOfRemoteDeviceCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TimeSynchronizationCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TimeSyncRequiredCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TimingPerformanceCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TimingRangeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TlsCipherSuitesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(TypeOfEndPointCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UdpPortForResponsesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UdpPortForUnsolicitedNullResponsesCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(UnsolicitedResponseConfirmationTimeoutCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(WriteAnyOtherCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(WriteClockCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(WriteLastRecordedTimeCapabilitiesType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(YesNoCapabilitiesType))]
    public partial class CapabilitiesBaseType
    {
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _note;
        
        [System.Xml.Serialization.XmlElementAttribute("note")]
        public System.Collections.ObjectModel.Collection<string> Note
        {
            get
            {
                return _note;
            }
            private set
            {
                _note = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Note collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NoteSpecified
        {
            get
            {
                return (this.Note.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="CapabilitiesBaseType" /> class.</para>
        /// </summary>
        public CapabilitiesBaseType()
        {
            this._note = new System.Collections.ObjectModel.Collection<string>();
        }
    }
}
