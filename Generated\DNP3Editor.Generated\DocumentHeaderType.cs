//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("documentHeaderType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DocumentHeaderType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("documentName")]
        public string DocumentName { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("documentDescription")]
        public string DocumentDescription { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<DocumentHeaderTypeRevisionHistory> _revisionHistory;
        
        [System.Xml.Serialization.XmlElementAttribute("revisionHistory")]
        public System.Collections.ObjectModel.Collection<DocumentHeaderTypeRevisionHistory> RevisionHistory
        {
            get
            {
                return _revisionHistory;
            }
            private set
            {
                _revisionHistory = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the RevisionHistory collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RevisionHistorySpecified
        {
            get
            {
                return (this.RevisionHistory.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="DocumentHeaderType" /> class.</para>
        /// </summary>
        public DocumentHeaderType()
        {
            this._revisionHistory = new System.Collections.ObjectModel.Collection<DocumentHeaderTypeRevisionHistory>();
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
    }
}
