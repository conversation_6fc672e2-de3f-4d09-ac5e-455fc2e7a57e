//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("connectionsSupportedCapabilitiesType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ConnectionsSupportedCapabilitiesType : CapabilitiesBaseType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("serial")]
        public EmptyElement Serial { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("network")]
        public EmptyElement Network { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<CustomType> _other;
        
        [System.Xml.Serialization.XmlElementAttribute("other")]
        public System.Collections.ObjectModel.Collection<CustomType> Other
        {
            get
            {
                return _other;
            }
            private set
            {
                _other = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Other collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OtherSpecified
        {
            get
            {
                return (this.Other.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="ConnectionsSupportedCapabilitiesType" /> class.</para>
        /// </summary>
        public ConnectionsSupportedCapabilitiesType()
        {
            this._other = new System.Collections.ObjectModel.Collection<CustomType>();
        }
    }
}
