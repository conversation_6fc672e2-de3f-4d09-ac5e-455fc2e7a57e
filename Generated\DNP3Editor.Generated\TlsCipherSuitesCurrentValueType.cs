//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("TLSCipherSuitesCurrentValueType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TlsCipherSuitesCurrentValueType : CurrentValueBaseType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("notApplicable")]
        public EmptyElement NotApplicable { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSRSAEncryptedAES128")]
        public EmptyElement TlsrsaEncryptedAes128 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSRSAEncryptedRC4")]
        public EmptyElement TlsrsaEncryptedRc4 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSRSAEncrypted3DES")]
        public EmptyElement TlsrsaEncrypted3Des { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSDHSignedDSSEncrypted3DES")]
        public EmptyElement TlsdhSignedDssEncrypted3Des { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSDHSignedRSAEncrypted3DES")]
        public EmptyElement TlsdhSignedRsaEncrypted3Des { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSDHESignedDSSEncrypted3DES")]
        public EmptyElement TlsdheSignedDssEncrypted3Des { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSDHESignedRSAEncrypted3DES")]
        public EmptyElement TlsdheSignedRsaEncrypted3Des { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSDHSignedDSSEncryptedAES128")]
        public EmptyElement TlsdhSignedDssEncryptedAes128 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSDHSignedDSSEncryptedAES256")]
        public EmptyElement TlsdhSignedDssEncryptedAes256 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSDHEncryptedAES128")]
        public EmptyElement TlsdhEncryptedAes128 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSDHEncryptedAES256")]
        public EmptyElement TlsdhEncryptedAes256 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("other")]
        public CustomType Other { get; set; }
    }
}
