using DNP3Editor.Generated;

namespace DNP3Editor.Services;

public class DocumentFactory
{
    public Dnp3DeviceProfileDocument CreateNewDocument()
    {
        return new Dnp3DeviceProfileDocument
        {
            SchemaVersion = "2.10.00",
            DocumentHeader = CreateDefaultHeader(),
            ReferenceDevice = CreateDefaultDevice()
        };
    }

    private static DocumentHeaderType CreateDefaultHeader()
    {
        return new DocumentHeaderType
        {
            DocumentName = "New DNP3 Device Profile",
            DocumentDescription = "Device profile created with DNP3 Editor",
            RevisionHistory = new[]
            {
                new DocumentHeaderTypeRevisionHistory
                {
                    Version = "1",
                    Date = DateTime.Today,
                    Author = Environment.UserName,
                    Reason = "Initial creation"
                }
            }
        };
    }

    private static Dnp3DeviceOptionalType CreateDefaultDevice()
    {
        return new Dnp3DeviceOptionalType
        {
            Description = "Reference Device",
            Configuration = new DnpConfigurationType
            {
                DeviceConfig = new DeviceConfigType
                {
                    DeviceName = new DeviceNameType
                    {
                        CurrentValue = new DeviceNameCurrentValueType
                        {
                            Value = "New Device"
                        }
                    },
                    DeviceFunction = new DeviceFunctionType
                    {
                        CurrentValue = new DeviceFunctionCurrentValueType
                        {
                            Outstation = new EmptyElement()
                        }
                    }
                }
            }
        };
    }
}
