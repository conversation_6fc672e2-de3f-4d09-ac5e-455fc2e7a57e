//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("tcpConnectionEstablishmentCurrentValueType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TcpConnectionEstablishmentCurrentValueType : CurrentValueBaseType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("allowsAll")]
        public EmptyElement AllowsAll { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("basedOnIPAddress")]
        public EmptyElement BasedOnIpAddress { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("basedOnListOfIPAddresses")]
        public EmptyElement BasedOnListOfIpAddresses { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("basedOnWildcardIPAddress")]
        public EmptyElement BasedOnWildcardIpAddress { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("basedOnListOfWildcardIPAddresses")]
        public EmptyElement BasedOnListOfWildcardIpAddresses { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("other")]
        public CustomType Other { get; set; }
    }
}
