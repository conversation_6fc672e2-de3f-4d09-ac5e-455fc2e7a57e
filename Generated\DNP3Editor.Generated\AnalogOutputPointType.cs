//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("analogOutputPointType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AnalogOutputPointType : OutputPointType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("defaultStaticVariation")]
        public AnalogOutputStaticVariationPerPointType DefaultStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultChangeEventVariation")]
        public AnalogOutputEventVariationPerPointType DefaultChangeEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultCommandEventVariation")]
        public AnalogOutputEventVariationPerPointType DefaultCommandEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxTimeSelectOperate")]
        public double MaxTimeSelectOperate { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the MaxTimeSelectOperate property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MaxTimeSelectOperateSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportedControlOperations")]
        public AnalogSupportedControlOperationsType SupportedControlOperations { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("minTransmittedValue")]
        public double MinTransmittedValue { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the MinTransmittedValue property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MinTransmittedValueSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxTransmittedValue")]
        public double MaxTransmittedValue { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the MaxTransmittedValue property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MaxTransmittedValueSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("scaleOffset")]
        public double ScaleOffset { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the ScaleOffset property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ScaleOffsetSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("scaleFactor")]
        public double ScaleFactor { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the ScaleFactor property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ScaleFactorSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("resolution")]
        public double Resolution { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the Resolution property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ResolutionSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("units")]
        public string Units { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("dnpData")]
        public AnalogOutputPointTypeDnpData DnpData { get; set; }
    }
}
