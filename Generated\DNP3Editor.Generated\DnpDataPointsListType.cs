//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("dnpDataPointsListType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DnpDataPointsListType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("binaryInputPoints")]
        public BinaryInputPointsType BinaryInputPoints { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("doubleBitInputPoints")]
        public DoubleBitInputPointsType DoubleBitInputPoints { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("binaryOutputPoints")]
        public BinaryOutputPointsType BinaryOutputPoints { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("counterPoints")]
        public CounterPointsType CounterPoints { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("analogInputPoints")]
        public AnalogInputPointsType AnalogInputPoints { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("analogOutputPoints")]
        public AnalogOutputPointsType AnalogOutputPoints { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("sequentialFiles")]
        public SequentialFileListType SequentialFiles { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("octetStringPoints")]
        public OctetStringPointsType OctetStringPoints { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("virtualTerminalPoints")]
        public VirtualTerminalPointsType VirtualTerminalPoints { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<DatasetPrototypeListType> _datasetPrototype;
        
        [System.Xml.Serialization.XmlElementAttribute("datasetPrototype")]
        public System.Collections.ObjectModel.Collection<DatasetPrototypeListType> DatasetPrototype
        {
            get
            {
                return _datasetPrototype;
            }
            private set
            {
                _datasetPrototype = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the DatasetPrototype collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DatasetPrototypeSpecified
        {
            get
            {
                return (this.DatasetPrototype.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="DnpDataPointsListType" /> class.</para>
        /// </summary>
        public DnpDataPointsListType()
        {
            this._datasetPrototype = new System.Collections.ObjectModel.Collection<DatasetPrototypeListType>();
            this._datasetDescriptor = new System.Collections.ObjectModel.Collection<DatasetDescriptorListType>();
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<DatasetDescriptorListType> _datasetDescriptor;
        
        [System.Xml.Serialization.XmlElementAttribute("datasetDescriptor")]
        public System.Collections.ObjectModel.Collection<DatasetDescriptorListType> DatasetDescriptor
        {
            get
            {
                return _datasetDescriptor;
            }
            private set
            {
                _datasetDescriptor = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the DatasetDescriptor collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DatasetDescriptorSpecified
        {
            get
            {
                return (this.DatasetDescriptor.Count != 0);
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
    }
}
