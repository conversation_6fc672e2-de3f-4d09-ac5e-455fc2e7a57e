//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("AnalogInputGroupTypeConfiguration", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AnalogInputGroupTypeConfiguration
    {
        
        [System.Xml.Serialization.XmlElementAttribute("defaultStaticVariation")]
        public DefaultAnalogInputStaticVariationType DefaultStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultEventVariation")]
        public DefaultAnalogInputEventVariationType DefaultEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("analogEventReportingMode")]
        public AnalogEventReportingModeType AnalogEventReportingMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("analogInputClass0ResponseMode")]
        public Class0ResponseModeType AnalogInputClass0ResponseMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("analogDeadbandAssignments")]
        public AnalogDeadbandAssignmentsType AnalogDeadbandAssignments { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("analogDeadbandAlgorithm")]
        public AnalogDeadbandAlgorithmType AnalogDeadbandAlgorithm { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultFrozenAnalogInputStaticVariation")]
        public DefaultFrozenAnalogInputStaticVariationType DefaultFrozenAnalogInputStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultFrozenAnalogInputEventVariation")]
        public DefaultFrozenAnalogInputEventVariationType DefaultFrozenAnalogInputEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenAnalogInputClass0ResponseMode")]
        public Class0ResponseModeType FrozenAnalogInputClass0ResponseMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenAnalogEventReportingMode")]
        public FrozenAnalogEventReportingModeType FrozenAnalogEventReportingMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("eventBufferPerObjectGroup")]
        public EventBufferPerObjectGroupType EventBufferPerObjectGroup { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("frozenEventBufferPerObjectGroup")]
        public EventBufferPerObjectGroupType FrozenEventBufferPerObjectGroup { get; set; }
    }
}
