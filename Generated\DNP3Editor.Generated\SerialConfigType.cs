//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("serialConfigType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SerialConfigType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("portName")]
        public PortNameType PortName { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("serialParameters")]
        public SerialParametersType SerialParameters { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("baudRate")]
        public BaudRateType BaudRate { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("flowControl")]
        public FlowControlType FlowControl { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("linkStatusInterval")]
        public LinkStatusIntervalType LinkStatusInterval { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportsCollisionAvoidance")]
        public SupportsCollisionAvoidanceType SupportsCollisionAvoidance { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("interCharacterTimeout")]
        public InterCharacterTimeoutType InterCharacterTimeout { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("interCharacterGap")]
        public InterCharacterGapType InterCharacterGap { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="SerialConfigType" /> class.</para>
        /// </summary>
        public SerialConfigType()
        {
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
    }
}
