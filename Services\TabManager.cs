using System.Collections.ObjectModel;
using DNP3Editor.Models;
using DNP3Editor.ViewModels;
using DNP3Editor.Views;
using DNP3Editor.Generated;

namespace DNP3Editor.Services;

public class TabManager(ObservableCollection<TabItemViewModel> tabs)
{
    private readonly ObservableCollection<TabItemViewModel> _tabs = tabs;

    public async Task LoadDefaultTabs(Dnp3DeviceProfileDocument document)
    {
        _tabs.Clear();

        if (document.ReferenceDevice?.Configuration != null)
        {
            var configTab = CreateDeviceConfigurationTab(document.ReferenceDevice.Configuration);
            _tabs.Add(configTab);
        }
    }

    public async Task<TabItemViewModel> GetOrCreateDataPointsTab(DnpDataPointsListType dataPointsList)
    {
        var existingTab = _tabs.FirstOrDefault(t => t.Id == "datapoints");
        if (existingTab != null)
        {
            return existingTab;
        }

        var viewModel = new DataPointsViewModel(dataPointsList);
        var tab = new TabItemViewModel
        {
            Id = "datapoints",
            Header = "Data Points",
            Content = new DataPointsView { DataContext = viewModel }
        };

        _tabs.Add(tab);
        return tab;
    }

    public void CloseTab(TabItemViewModel tab)
    {
        if (tab.CanClose && _tabs.Contains(tab))
        {
            _tabs.Remove(tab);
        }
    }

    private static TabItemViewModel CreateDeviceConfigurationTab(DnpConfigurationType configuration)
    {
        var viewModel = new DeviceConfigurationViewModel(configuration);
        return new TabItemViewModel
        {
            Id = "configuration",
            Header = "Device Configuration",
            Content = new DeviceConfigurationView { DataContext = viewModel }
        };
    }
}
