//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("outstationSetsIIN14CapabilitiesType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OutstationSetsIin14CapabilitiesType : CapabilitiesBaseType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("never")]
        public EmptyElement Never { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("atStartup")]
        public EmptyElement AtStartup { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("periodicallyFixed")]
        public string PeriodicallyFixed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("periodicallyRange")]
        public PositiveIntRangeType PeriodicallyRange { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _periodicallySelectable;
        
        [System.Xml.Serialization.XmlElementAttribute("periodicallySelectable")]
        public System.Collections.ObjectModel.Collection<string> PeriodicallySelectable
        {
            get
            {
                return _periodicallySelectable;
            }
            private set
            {
                _periodicallySelectable = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the PeriodicallySelectable collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PeriodicallySelectableSpecified
        {
            get
            {
                return (this.PeriodicallySelectable.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="OutstationSetsIin14CapabilitiesType" /> class.</para>
        /// </summary>
        public OutstationSetsIin14CapabilitiesType()
        {
            this._periodicallySelectable = new System.Collections.ObjectModel.Collection<string>();
            this._afterLastTimeSyncSelectable = new System.Collections.ObjectModel.Collection<string>();
            this._whenTimeErrorExceedsSelectable = new System.Collections.ObjectModel.Collection<string>();
        }
        
        [System.Xml.Serialization.XmlElementAttribute("afterLastTimeSyncFixed")]
        public string AfterLastTimeSyncFixed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("afterLastTimeSyncRange")]
        public PositiveIntRangeType AfterLastTimeSyncRange { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _afterLastTimeSyncSelectable;
        
        [System.Xml.Serialization.XmlElementAttribute("afterLastTimeSyncSelectable")]
        public System.Collections.ObjectModel.Collection<string> AfterLastTimeSyncSelectable
        {
            get
            {
                return _afterLastTimeSyncSelectable;
            }
            private set
            {
                _afterLastTimeSyncSelectable = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the AfterLastTimeSyncSelectable collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AfterLastTimeSyncSelectableSpecified
        {
            get
            {
                return (this.AfterLastTimeSyncSelectable.Count != 0);
            }
        }
        
        [System.Xml.Serialization.XmlElementAttribute("whenTimeErrorExceedsFixed")]
        public string WhenTimeErrorExceedsFixed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("whenTimeErrorExceedsRange")]
        public PositiveIntRangeType WhenTimeErrorExceedsRange { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _whenTimeErrorExceedsSelectable;
        
        [System.Xml.Serialization.XmlElementAttribute("whenTimeErrorExceedsSelectable")]
        public System.Collections.ObjectModel.Collection<string> WhenTimeErrorExceedsSelectable
        {
            get
            {
                return _whenTimeErrorExceedsSelectable;
            }
            private set
            {
                _whenTimeErrorExceedsSelectable = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the WhenTimeErrorExceedsSelectable collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool WhenTimeErrorExceedsSelectableSpecified
        {
            get
            {
                return (this.WhenTimeErrorExceedsSelectable.Count != 0);
            }
        }
    }
}
