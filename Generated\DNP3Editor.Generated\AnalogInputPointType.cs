//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("analogInputPointType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AnalogInputPointType : InputPointType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("defaultStaticVariation")]
        public AnalogInputStaticVariationPerPointType DefaultStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultEventVariation")]
        public AnalogInputEventVariationPerPointType DefaultEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("analogEventReportingMode")]
        public AnalogInputEventReportingModePerPointType AnalogEventReportingMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("analogDeadbandAssignments")]
        public AnalogDeadbandAssignmentsPerPointType AnalogDeadbandAssignments { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("analogDeadbandAlgorithm")]
        public AnalogDeadbandAlgorithmPerPointType AnalogDeadbandAlgorithm { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("minIntegerTransmittedValue")]
        public string MinIntegerTransmittedValue { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxIntegerTransmittedValue")]
        public string MaxIntegerTransmittedValue { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("minFloatTransmittedValue")]
        public double MinFloatTransmittedValue { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the MinFloatTransmittedValue property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MinFloatTransmittedValueSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxFloatTransmittedValue")]
        public double MaxFloatTransmittedValue { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the MaxFloatTransmittedValue property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MaxFloatTransmittedValueSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("scaleOffset")]
        public double ScaleOffset { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the ScaleOffset property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ScaleOffsetSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("scaleFactor")]
        public double ScaleFactor { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the ScaleFactor property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ScaleFactorSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("resolution")]
        public double Resolution { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the Resolution property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ResolutionSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("units")]
        public string Units { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("dnpData")]
        public AnalogInputPointTypeDnpData DnpData { get; set; }
    }
}
