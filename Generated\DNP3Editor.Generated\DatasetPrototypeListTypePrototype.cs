//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("DatasetPrototypeListTypePrototype", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DatasetPrototypeListTypePrototype
    {
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("id")]
        public uint Id { get; set; }
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("uuid")]
        public string Uuid { get; set; }
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("description")]
        public string Description { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("namespace")]
        public string Namespace { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("name")]
        public string Name { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<DatasetPrototypeListTypePrototypeDataElement> _dataElement;
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("dataElement")]
        public System.Collections.ObjectModel.Collection<DatasetPrototypeListTypePrototypeDataElement> DataElement
        {
            get
            {
                return _dataElement;
            }
            private set
            {
                _dataElement = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="DatasetPrototypeListTypePrototype" /> class.</para>
        /// </summary>
        public DatasetPrototypeListTypePrototype()
        {
            this._dataElement = new System.Collections.ObjectModel.Collection<DatasetPrototypeListTypePrototypeDataElement>();
        }
    }
}
