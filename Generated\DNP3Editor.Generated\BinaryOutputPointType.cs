//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("binaryOutputPointType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class BinaryOutputPointType : OutputPointType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("defaultStaticVariation")]
        public BinaryOutputStaticVariationPerPointType DefaultStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultChangeEventVariation")]
        public BinaryOutputEventVariationPerPointType DefaultChangeEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultCommandEventVariation")]
        public BinaryOutputEventVariationPerPointType DefaultCommandEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("minimumPulseWidth")]
        public double MinimumPulseWidth { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the MinimumPulseWidth property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MinimumPulseWidthSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maximumPulseWidth")]
        public double MaximumPulseWidth { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the MaximumPulseWidth property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MaximumPulseWidthSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportedControlOperations")]
        public BinarySupportedControlOperationsType SupportedControlOperations { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxTimeSelectOperate")]
        public double MaxTimeSelectOperate { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the MaxTimeSelectOperate property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MaxTimeSelectOperateSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("nameState0")]
        public string NameState0 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("nameState1")]
        public string NameState1 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("dnpData")]
        public BinaryOutputPointTypeDnpData DnpData { get; set; }
    }
}
