//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("mappingRuleBasicType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    public enum MappingRuleBasicType
    {
        
        [System.Xml.Serialization.XmlEnumAttribute("BOOLEAN_TO_BI")]
        BooleanToBi,
        
        [System.Xml.Serialization.XmlEnumAttribute("BOOLEAN_TO_BO")]
        BooleanToBo,
        
        [System.Xml.Serialization.XmlEnumAttribute("BOOLEAN_TO_BO_CONST")]
        BooleanToBoConst,
        
        [System.Xml.Serialization.XmlEnumAttribute("CURRENCY_TO_OCT")]
        CurrencyToOct,
        
        [System.Xml.Serialization.XmlEnumAttribute("DESC_TO_PROFILE")]
        DescToProfile,
        
        [System.Xml.Serialization.XmlEnumAttribute("DPS_TO_2_BI")]
        DpsTo2Bi,
        
        [System.Xml.Serialization.XmlEnumAttribute("DPS_TO_DBBI")]
        DpsToDbbi,
        
        [System.Xml.Serialization.XmlEnumAttribute("DPS_TO_BO")]
        DpsToBo,
        
        [System.Xml.Serialization.XmlEnumAttribute("ENUM_HVREF_TO_PROFILE")]
        EnumHvrefToProfile,
        
        [System.Xml.Serialization.XmlEnumAttribute("ENUM_TO_AI")]
        EnumToAi,
        
        [System.Xml.Serialization.XmlEnumAttribute("ENUM_TO_AO")]
        EnumToAo,
        
        [System.Xml.Serialization.XmlEnumAttribute("ENUM_TO_AO_CONST")]
        EnumToAoConst,
        
        [System.Xml.Serialization.XmlEnumAttribute("ENUM_TO_BI")]
        EnumToBi,
        
        [System.Xml.Serialization.XmlEnumAttribute("ENUM_TO_BO")]
        EnumToBo,
        
        [System.Xml.Serialization.XmlEnumAttribute("ENUM_TO_BO_PROFILE")]
        EnumToBoProfile,
        
        [System.Xml.Serialization.XmlEnumAttribute("ENUM_TO_DBBI")]
        EnumToDbbi,
        
        [System.Xml.Serialization.XmlEnumAttribute("FLOAT_TO_AI")]
        FloatToAi,
        
        [System.Xml.Serialization.XmlEnumAttribute("FLOAT_TO_AI_FP")]
        FloatToAiFp,
        
        [System.Xml.Serialization.XmlEnumAttribute("FLOAT_TO_AO")]
        FloatToAo,
        
        [System.Xml.Serialization.XmlEnumAttribute("FLOAT_TO_AO_FP")]
        FloatToAoFp,
        
        [System.Xml.Serialization.XmlEnumAttribute("FLOAT_TO_AO_FP_CONST")]
        FloatToAoFpConst,
        
        [System.Xml.Serialization.XmlEnumAttribute("FRZ_ENA_TO_FRZWT")]
        FrzEnaToFrzwt,
        
        [System.Xml.Serialization.XmlEnumAttribute("FRZ_PD_TO_TODINT")]
        FrzPdToTodint,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_RMS_CYC_TO_PROFILE")]
        IntRmsCycToProfile,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_AI")]
        IntToAi,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_AI_CONST")]
        IntToAiConst,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_AI_DEADBAND")]
        IntToAiDeadband,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_AI_FP")]
        IntToAiFp,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_AO")]
        IntToAo,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_AO_CONST")]
        IntToAoConst,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_AO_FP")]
        IntToAoFp,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_AO_PROFILE")]
        IntToAoProfile,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_CTR")]
        IntToCtr,
        
        [System.Xml.Serialization.XmlEnumAttribute("INT_TO_FRZ_CTR")]
        IntToFrzCtr,
        
        [System.Xml.Serialization.XmlEnumAttribute("MULTI_CELL_TO_AIFP")]
        MultiCellToAifp,
        
        [System.Xml.Serialization.XmlEnumAttribute("MULTI_INT_TO_AI")]
        MultiIntToAi,
        
        [System.Xml.Serialization.XmlEnumAttribute("OCT_TO_OCT")]
        OctToOct,
        
        [System.Xml.Serialization.XmlEnumAttribute("PULSE_CONFIG_TO_CROB")]
        PulseConfigToCrob,
        
        [System.Xml.Serialization.XmlEnumAttribute("QUALITY_TO_BIN_FLAG")]
        QualityToBinFlag,
        
        [System.Xml.Serialization.XmlEnumAttribute("QUALITY_TO_CTR_FLAG")]
        QualityToCtrFlag,
        
        [System.Xml.Serialization.XmlEnumAttribute("QUALITY_TO_ANA_FLAG")]
        QualityToAnaFlag,
        
        [System.Xml.Serialization.XmlEnumAttribute("QUALITY_TO_OCT")]
        QualityToOct,
        
        [System.Xml.Serialization.XmlEnumAttribute("STRING_TO_OCT ")]
        StringToOct,
        
        [System.Xml.Serialization.XmlEnumAttribute("TIME_AND_DATE_TO_AO")]
        TimeAndDateToAo,
        
        [System.Xml.Serialization.XmlEnumAttribute("TIME_IEC_ONLY")]
        TimeIecOnly,
        
        [System.Xml.Serialization.XmlEnumAttribute("TIME_TO_AO")]
        TimeToAo,
        
        [System.Xml.Serialization.XmlEnumAttribute("TIME_TO_FRZ_CTR")]
        TimeToFrzCtr,
        
        [System.Xml.Serialization.XmlEnumAttribute("TIME_TO_TIME")]
        TimeToTime,
        
        [System.Xml.Serialization.XmlEnumAttribute("UNITS_TO_AI_CONST")]
        UnitsToAiConst,
        
        [System.Xml.Serialization.XmlEnumAttribute("UNITS_TO_OCT")]
        UnitsToOct,
        
        [System.Xml.Serialization.XmlEnumAttribute("UNITS_TO_PROFILE")]
        UnitsToProfile,
    }
}
