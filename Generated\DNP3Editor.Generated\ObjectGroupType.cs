//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("objectGroupType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputPointsType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogOutputGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogOutputPointsType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryInputGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryInputPointsType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryOutputGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryOutputPointsType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterPointsType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DoubleBitInputGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DoubleBitInputPointsType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InputGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OctetStringGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OctetStringPointsType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OutputGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(VirtualTerminalGroupType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(VirtualTerminalPointsType))]
    public partial class ObjectGroupType
    {
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="ObjectGroupType" /> class.</para>
        /// </summary>
        public ObjectGroupType()
        {
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
    }
}
