using Microsoft.Win32;
using System.Windows.Forms;

namespace DNP3Editor.Services;

public class FileDialogService : IFileDialogService
{
    public async Task<string?> ShowOpenFileDialogAsync(string title, string filter)
    {
        return await Task.Run(() =>
        {
            var dialog = new OpenFileDialog
            {
                Title = title,
                Filter = filter
            };

            return dialog.ShowDialog() == true ? dialog.FileName : null;
        });
    }

    public async Task<string?> ShowSaveFileDialogAsync(string title, string filter, string defaultFileName = "")
    {
        return await Task.Run(() =>
        {
            var dialog = new SaveFileDialog
            {
                Title = title,
                Filter = filter,
                FileName = defaultFileName
            };

            return dialog.ShowDialog() == true ? dialog.FileName : null;
        });
    }

    public async Task<string?> ShowFolderDialogAsync(string title)
    {
        return await Task.Run(() =>
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = title
            };

            return dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK ? dialog.SelectedPath : null;
        });
    }
}
