//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("DatasetDescriptorListTypeDatasetPointDnpData", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DatasetDescriptorListTypeDatasetPointDnpData
    {
        
        [System.Xml.Serialization.XmlElementAttribute("vstr")]
        public string Vstr { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("uint")]
        public uint Uint { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the Uint property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UintSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("int")]
        public int Int { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the Int property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IntSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("flt")]
        public float Flt { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the Flt property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FltSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("ostr")]
        public string Ostr { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("bstr")]
        public uint Bstr { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the Bstr property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool BstrSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("time", DataType="dateTime")]
        public System.DateTime Time { get; set; }
        
        /// <summary>
        /// <para xml:lang="en">Gets or sets a value indicating whether the Time property is specified.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TimeSpecified { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("uncd")]
        public string Uncd { get; set; }
    }
}
