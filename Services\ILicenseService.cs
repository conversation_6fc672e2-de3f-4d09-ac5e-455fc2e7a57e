namespace DNP3Editor.Services;

public interface ILicenseService
{
    bool IsLicenseValid();
    bool ValidateLicense(string licenseKey);
    LicenseInfo GetLicenseInfo();
    Task<bool> ActivateLicenseAsync(string licenseKey);
}

public class LicenseInfo
{
    public string LicenseKey { get; set; } = string.Empty;
    public string LicensedTo { get; set; } = string.Empty;
    public DateTime ExpirationDate { get; set; }
    public bool IsValid { get; set; }
    public LicenseType Type { get; set; }
}

public enum LicenseType
{
    Trial,
    Standard,
    Professional,
    Enterprise
}
