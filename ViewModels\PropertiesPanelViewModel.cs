using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.ObjectModel;
using DNP3Editor.Models;

namespace DNP3Editor.ViewModels;

public partial class PropertiesPanelViewModel : ObservableObject
{
    [ObservableProperty]
    private string selectedObjectName = "No Selection";

    [ObservableProperty]
    private string selectedObjectType = "";

    public ObservableCollection<PropertyItem> Properties { get; } = [];

    public void LoadProperties(NavigationNode node)
    {
        SelectedObjectName = node.Name;
        SelectedObjectType = node.NodeType.ToString();

        Properties.Clear();

        if (node.Data != null)
        {
            LoadPropertiesFromObject(node.Data);
        }
    }

    private void LoadPropertiesFromObject(object obj)
    {
        var type = obj.GetType();
        var properties = type.GetProperties();

        foreach (var prop in properties)
        {
            if (prop.CanRead && IsDisplayableProperty(prop))
            {
                var value = prop.GetValue(obj);
                Properties.Add(new PropertyItem
                {
                    Name = prop.Name,
                    Value = value?.ToString() ?? "",
                    Type = prop.PropertyType.Name,
                    IsReadOnly = !prop.CanWrite
                });
            }
        }
    }

    private static bool IsDisplayableProperty(System.Reflection.PropertyInfo prop)
    {
        // Filter out complex navigation properties and internal properties
        var type = prop.PropertyType;
        
        return type.IsPrimitive || 
               type == typeof(string) || 
               type == typeof(DateTime) ||
               type == typeof(bool) ||
               type.IsEnum ||
               (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>));
    }
}

public class PropertyItem
{
    public string Name { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool IsReadOnly { get; set; }
}
