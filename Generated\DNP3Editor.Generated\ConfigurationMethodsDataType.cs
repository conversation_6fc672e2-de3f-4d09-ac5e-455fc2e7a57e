//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("configurationMethodsDataType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ConfigurationMethodsDataType
    {
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _note;
        
        [System.Xml.Serialization.XmlElementAttribute("note")]
        public System.Collections.ObjectModel.Collection<string> Note
        {
            get
            {
                return _note;
            }
            private set
            {
                _note = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Note collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NoteSpecified
        {
            get
            {
                return (this.Note.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="ConfigurationMethodsDataType" /> class.</para>
        /// </summary>
        public ConfigurationMethodsDataType()
        {
            this._note = new System.Collections.ObjectModel.Collection<string>();
            this._software = new System.Collections.ObjectModel.Collection<SoftwareType>();
            this._other = new System.Collections.ObjectModel.Collection<CustomType>();
        }
        
        [System.Xml.Serialization.XmlElementAttribute("xmlViaFileTransfer")]
        public EmptyElement XmlViaFileTransfer { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("xmlViaOtherTransportMechanism")]
        public EmptyElement XmlViaOtherTransportMechanism { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("terminal")]
        public EmptyElement Terminal { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("proprietaryFileViaFileTransfer")]
        public EmptyElement ProprietaryFileViaFileTransfer { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("proprietaryFileViaOtherTransportMechanism")]
        public EmptyElement ProprietaryFileViaOtherTransportMechanism { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("direct")]
        public EmptyElement Direct { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("factory")]
        public EmptyElement Factory { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("protocol")]
        public EmptyElement Protocol { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<SoftwareType> _software;
        
        [System.Xml.Serialization.XmlElementAttribute("software")]
        public System.Collections.ObjectModel.Collection<SoftwareType> Software
        {
            get
            {
                return _software;
            }
            private set
            {
                _software = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Software collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SoftwareSpecified
        {
            get
            {
                return (this.Software.Count != 0);
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<CustomType> _other;
        
        [System.Xml.Serialization.XmlElementAttribute("other")]
        public System.Collections.ObjectModel.Collection<CustomType> Other
        {
            get
            {
                return _other;
            }
            private set
            {
                _other = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Other collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OtherSpecified
        {
            get
            {
                return (this.Other.Count != 0);
            }
        }
    }
}
