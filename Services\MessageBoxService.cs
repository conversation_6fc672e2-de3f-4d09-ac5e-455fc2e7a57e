using System.Windows;

namespace DNP3Editor.Services;

public class MessageBoxService : IMessageBoxService
{
    public async Task<MessageBoxResult> ShowQuestionAsync(string title, string message, MessageBoxButton buttons = MessageBoxButton.YesNo)
    {
        return await Task.Run(() => System.Windows.MessageBox.Show(message, title, buttons, MessageBoxImage.Question));
    }

    public async Task ShowInformationAsync(string title, string message)
    {
        await Task.Run(() => System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information));
    }

    public async Task ShowWarningAsync(string title, string message)
    {
        await Task.Run(() => System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning));
    }

    public async Task ShowErrorAsync(string title, string message)
    {
        await Task.Run(() => System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error));
    }
}
