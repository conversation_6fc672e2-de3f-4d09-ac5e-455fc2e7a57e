//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("outstationConfigType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OutstationConfigType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("applicationLayerConfirmTimeout")]
        public ApplicationLayerConfirmTimeoutType ApplicationLayerConfirmTimeout { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("timeSyncRequired")]
        public TimeSyncRequiredType TimeSyncRequired { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("deviceTroubleBit")]
        public DeviceTroubleBitType DeviceTroubleBit { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("fileHandleTimeout")]
        public FileHandleTimeoutType FileHandleTimeout { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("eventBufferOverflowBehavior")]
        public EventBufferOverflowBehaviorType EventBufferOverflowBehavior { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("eventBufferOrganization")]
        public EventBufferOrganizationType EventBufferOrganization { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("sendsMultiFragmentResponses")]
        public SendsMultiFragmentResponsesType SendsMultiFragmentResponses { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requestsLastFragmentConfirmation")]
        public RequestsLastFragmentConfirmationType RequestsLastFragmentConfirmation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("settingsPreservedThroughDeviceRestart")]
        public SettingsPreservedThroughDeviceRestartType SettingsPreservedThroughDeviceRestart { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("configurationSignatureSupported")]
        public ConfigurationSignatureSupportedType ConfigurationSignatureSupported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requestsApplicationConfirmation")]
        public RequestsApplicationConfirmationType RequestsApplicationConfirmation { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="OutstationConfigType" /> class.</para>
        /// </summary>
        public OutstationConfigType()
        {
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
    }
}
