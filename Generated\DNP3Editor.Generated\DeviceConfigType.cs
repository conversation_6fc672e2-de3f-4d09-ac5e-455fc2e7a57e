//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("deviceConfigType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DeviceConfigType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("deviceFunction")]
        public DeviceFunctionType DeviceFunction { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("vendorName")]
        public VendorNameType VendorName { get; set; }
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("deviceName")]
        public DeviceNameType DeviceName { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("hardwareVersion")]
        public HardwareVersionType HardwareVersion { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("softwareVersion")]
        public SoftwareVersionType SoftwareVersion { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("documentVersionNumber")]
        public DocumentVersionNumberType DocumentVersionNumber { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("dnpLevelSupported")]
        public DnpLevelSupportedType DnpLevelSupported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportedFunctionBlocks")]
        public SupportedFunctionBlocksType SupportedFunctionBlocks { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("notableAdditions")]
        public NotableAdditionsType NotableAdditions { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("configurationMethods")]
        public ConfigurationMethodsType ConfigurationMethods { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("onlineXmlFileNames")]
        public OnlineXmlFileNamesType OnlineXmlFileNames { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("offlineXmlFileNames")]
        public OfflineXmlFileNamesType OfflineXmlFileNames { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("connectionsSupported")]
        public ConnectionsSupportedType ConnectionsSupported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("conformanceTesting")]
        public ConformanceTestingType ConformanceTesting { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="DeviceConfigType" /> class.</para>
        /// </summary>
        public DeviceConfigType()
        {
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
    }
}
