//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("iec61850DeviceMappingType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class Iec61850DeviceMappingType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("accessPoint")]
        public string AccessPoint { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<Iec61850RuleMappingType> _iec61850RuleMapping;
        
        [System.Xml.Serialization.XmlElementAttribute("iec61850RuleMapping")]
        public System.Collections.ObjectModel.Collection<Iec61850RuleMappingType> Iec61850RuleMapping
        {
            get
            {
                return _iec61850RuleMapping;
            }
            private set
            {
                _iec61850RuleMapping = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Iec61850RuleMapping collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool Iec61850RuleMappingSpecified
        {
            get
            {
                return (this.Iec61850RuleMapping.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="Iec61850DeviceMappingType" /> class.</para>
        /// </summary>
        public Iec61850DeviceMappingType()
        {
            this._iec61850RuleMapping = new System.Collections.ObjectModel.Collection<Iec61850RuleMappingType>();
            this._iec61850EquationMapping = new System.Collections.ObjectModel.Collection<Iec61850EquationMappingType>();
            this._enum = new System.Collections.ObjectModel.Collection<EnumType>();
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<Iec61850EquationMappingType> _iec61850EquationMapping;
        
        [System.Xml.Serialization.XmlElementAttribute("iec61850EquationMapping")]
        public System.Collections.ObjectModel.Collection<Iec61850EquationMappingType> Iec61850EquationMapping
        {
            get
            {
                return _iec61850EquationMapping;
            }
            private set
            {
                _iec61850EquationMapping = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Iec61850EquationMapping collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool Iec61850EquationMappingSpecified
        {
            get
            {
                return (this.Iec61850EquationMapping.Count != 0);
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<EnumType> _enum;
        
        [System.Xml.Serialization.XmlElementAttribute("enum")]
        public System.Collections.ObjectModel.Collection<EnumType> Enum
        {
            get
            {
                return _enum;
            }
            private set
            {
                _enum = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Enum collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool EnumSpecified
        {
            get
            {
                return (this.Enum.Count != 0);
            }
        }
    }
}
