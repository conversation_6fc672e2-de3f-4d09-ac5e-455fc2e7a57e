//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("additionalCriticalFCsCapabilitiesType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AdditionalCriticalFCsCapabilitiesType : CapabilitiesBaseType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("FC0")]
        public EmptyElement Fc0 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC1")]
        public EmptyElement Fc1 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC7")]
        public EmptyElement Fc7 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC8")]
        public EmptyElement Fc8 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC9")]
        public EmptyElement Fc9 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC10")]
        public EmptyElement Fc10 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC11")]
        public EmptyElement Fc11 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC12")]
        public EmptyElement Fc12 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC22")]
        public EmptyElement Fc22 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC23")]
        public EmptyElement Fc23 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC25")]
        public EmptyElement Fc25 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC26")]
        public EmptyElement Fc26 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC27")]
        public EmptyElement Fc27 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC28")]
        public EmptyElement Fc28 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC30")]
        public EmptyElement Fc30 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC129")]
        public EmptyElement Fc129 { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("FC130")]
        public EmptyElement Fc130 { get; set; }
    }
}
