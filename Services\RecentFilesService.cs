using System.IO;
using DNP3Editor.Models;

namespace DNP3Editor.Services;

public class RecentFilesService
{
    private const int MaxRecentFiles = 10;
    private readonly string _settingsPath;

    public RecentFilesService()
    {
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var appFolder = Path.Combine(appDataPath, "DNP3Editor");
        Directory.CreateDirectory(appFolder);
        _settingsPath = Path.Combine(appFolder, "recent-files.json");
    }

    public List<RecentFileViewModel> GetRecentFiles()
    {
        try
        {
            if (!File.Exists(_settingsPath))
                return [];

            var json = File.ReadAllText(_settingsPath);
            var files = System.Text.Json.JsonSerializer.Deserialize<List<RecentFileData>>(json);
            
            return files?
                .Where(f => File.Exists(f.FilePath))
                .OrderByDescending(f => f.LastAccessed)
                .Select(f => new RecentFileViewModel 
                { 
                    FilePath = f.FilePath, 
                    LastAccessed = f.LastAccessed 
                })
                .ToList() ?? [];
        }
        catch
        {
            return [];
        }
    }

    public void AddRecentFile(string filePath)
    {
        var recentFiles = GetRecentFiles();
        
        // Remove if already exists
        recentFiles.RemoveAll(f => f.FilePath.Equals(filePath, StringComparison.OrdinalIgnoreCase));
        
        // Add to top
        recentFiles.Insert(0, new RecentFileViewModel 
        { 
            FilePath = filePath, 
            LastAccessed = DateTime.Now 
        });
        
        // Limit to max files
        if (recentFiles.Count > MaxRecentFiles)
        {
            recentFiles = recentFiles.Take(MaxRecentFiles).ToList();
        }

        SaveRecentFiles(recentFiles);
    }

    private static readonly System.Text.Json.JsonSerializerOptions JsonOptions = new()
    {
        WriteIndented = true
    };

    private void SaveRecentFiles(List<RecentFileViewModel> recentFiles)
    {
        try
        {
            var data = recentFiles.Select(f => new RecentFileData 
            { 
                FilePath = f.FilePath, 
                LastAccessed = f.LastAccessed 
            }).ToList();
            
            var json = System.Text.Json.JsonSerializer.Serialize(data, JsonOptions);
            
            File.WriteAllText(_settingsPath, json);
        }
        catch
        {
            // Ignore save errors
        }
    }

    private class RecentFileData
    {
        public string FilePath { get; set; } = string.Empty;
        public DateTime LastAccessed { get; set; }
    }
}
