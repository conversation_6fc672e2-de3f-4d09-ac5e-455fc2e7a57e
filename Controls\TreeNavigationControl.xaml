<UserControl x:Class="DNP3Editor.Controls.TreeNavigationControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Grid>
        <TreeView ItemsSource="{Binding RootNodes}" 
                  SelectedItemChanged="TreeView_SelectedItemChanged">
            <TreeView.ItemTemplate>
                <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                    <StackPanel Orientation="Horizontal">
                        <ui:SymbolIcon Symbol="{Binding IconSymbol}" 
                                       Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding DisplayName}" VerticalAlignment="Center"/>
                    </StackPanel>
                    <HierarchicalDataTemplate.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <ui:SymbolIcon Symbol="{Binding IconSymbol}" 
                                               Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding DisplayName}" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </HierarchicalDataTemplate.ItemTemplate>
                </HierarchicalDataTemplate>
            </TreeView.ItemTemplate>
        </TreeView>
    </Grid>
</UserControl>
