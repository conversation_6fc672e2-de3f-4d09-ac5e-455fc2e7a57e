//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("analogOutputPointsType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AnalogOutputPointsType : OutputGroupType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("configuration")]
        public AnalogOutputPointsTypeConfiguration Configuration { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<AnalogOutputPointType> _dataPoints;
        
        [System.Xml.Serialization.XmlArrayAttribute("dataPoints")]
        [System.Xml.Serialization.XmlArrayItemAttribute("analogOutput", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
        public System.Collections.ObjectModel.Collection<AnalogOutputPointType> DataPoints
        {
            get
            {
                return _dataPoints;
            }
            private set
            {
                _dataPoints = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the DataPoints collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DataPointsSpecified
        {
            get
            {
                return (this.DataPoints.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="AnalogOutputPointsType" /> class.</para>
        /// </summary>
        public AnalogOutputPointsType()
        {
            this._dataPoints = new System.Collections.ObjectModel.Collection<AnalogOutputPointType>();
        }
    }
}
