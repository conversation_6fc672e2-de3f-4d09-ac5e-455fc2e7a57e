using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;
using DNP3Editor.Models;
using DNP3Editor.Services;


namespace DNP3Editor.ViewModels;

public partial class MainWindowViewModel : ObservableObject
{
    private readonly IXmlDocumentService _documentService;
    private readonly ILicenseService _licenseService;
    private readonly IValidationService _validationService;
    private readonly IFileDialogService _fileDialogService;
    private readonly IMessageBoxService _messageBoxService;
    private readonly ILogger<MainWindowViewModel> _logger;

    [ObservableProperty]
    private string windowTitle = "DNP3 Device Profile Editor";

    [ObservableProperty]
    private string statusMessage = "Ready";

    [ObservableProperty]
    private string validationStatus = "Not Validated";

    [ObservableProperty]
    private string documentStatus = "No Document";

    [ObservableProperty]
    private bool isNavigationTreeVisible = true;

    [ObservableProperty]
    private bool isPropertiesPanelVisible = true;

    [ObservableProperty]
    private bool isOutputWindowVisible = true;

    [ObservableProperty]
    private DNP3DeviceProfileDocument? currentDocument;

    [ObservableProperty]
    private string? currentFilePath;

    [ObservableProperty]
    private bool isDocumentModified;

    [ObservableProperty]
    private TabItemViewModel? selectedTab;

    public ObservableCollection<TabItemViewModel> OpenTabs { get; } = new();
    public ObservableCollection<RecentFileViewModel> RecentFiles { get; } = new();

    // Child ViewModels
    public TreeNavigationViewModel NavigationViewModel { get; }
    public PropertiesPanelViewModel PropertiesViewModel { get; }
    public OutputWindowViewModel OutputViewModel { get; }

    public MainWindowViewModel(
        IXmlDocumentService documentService,
        ILicenseService licenseService,
        IValidationService validationService,
        IFileDialogService fileDialogService,
        IMessageBoxService messageBoxService,
        ILogger<MainWindowViewModel> logger)
    {
        _documentService = documentService;
        _licenseService = licenseService;
        _validationService = validationService;
        _fileDialogService = fileDialogService;
        _messageBoxService = messageBoxService;
        _logger = logger;

        // Initialize child view models
        NavigationViewModel = new TreeNavigationViewModel();
        PropertiesViewModel = new PropertiesPanelViewModel();
        OutputViewModel = new OutputWindowViewModel();

        // Subscribe to events
        NavigationViewModel.NodeSelected += OnNavigationNodeSelected;
        
        // Initialize
        LoadRecentFiles();
        CheckLicense();
        UpdateWindowTitle();
    }

    [RelayCommand]
    private async Task NewDocument()
    {
        if (!await ConfirmUnsavedChanges()) return;

        try
        {
            CurrentDocument = new DNP3DeviceProfileDocument
            {
                SchemaVersion = "2.10.00",
                DocumentHeader = new DocumentHeaderType
                {
                    DocumentName = "New DNP3 Device Profile",
                    DocumentDescription = "Device profile created with DNP3 Editor"
                },
                ReferenceDevice = new DNP3DeviceOptionalType
                {
                    Description = "Reference Device",
                    Configuration = new DNP3ConfigurationType()
                }
            };

            CurrentFilePath = null;
            IsDocumentModified = false;
            StatusMessage = "New document created";
            DocumentStatus = "New Document";
            
            NavigationViewModel.LoadDocument(CurrentDocument);
            OpenDefaultTabs();
            
            _logger.LogInformation("New document created");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating new document");
            await _messageBoxService.ShowErrorAsync("Error creating new document", ex.Message);
        }
    }

    [RelayCommand]
    private async Task OpenDocument()
    {
        if (!await ConfirmUnsavedChanges()) return;

        var filePath = await _fileDialogService.ShowOpenFileDialogAsync(
            "Open DNP3 Device Profile",
            "XML Files (*.xml)|*.xml|All Files (*.*)|*.*");

        if (string.IsNullOrEmpty(filePath)) return;

        await LoadDocument(filePath);
    }

    [RelayCommand]
    private async Task SaveDocument()
    {
        if (CurrentDocument == null) return;

        if (string.IsNullOrEmpty(CurrentFilePath))
        {
            await SaveAsDocument();
            return;
        }

        await SaveDocumentToFile(CurrentFilePath);
    }

    [RelayCommand]
    private async Task SaveAsDocument()
    {
        if (CurrentDocument == null) return;

        var filePath = await _fileDialogService.ShowSaveFileDialogAsync(
            "Save DNP3 Device Profile",
            "XML Files (*.xml)|*.xml",
            "DeviceProfile.xml");

        if (string.IsNullOrEmpty(filePath)) return;

        await SaveDocumentToFile(filePath);
    }

    [RelayCommand]
    private async Task ValidateDocument()
    {
        if (CurrentDocument == null)
        {
            ValidationStatus = "No Document";
            return;
        }

        try
        {
            var validationResult = await _validationService.ValidateDocumentAsync(CurrentDocument);
            
            ValidationStatus = validationResult.IsValid ? "Valid" : $"Errors: {validationResult.Errors.Count}";
            
            OutputViewModel.Clear();
            OutputViewModel.AddMessage("Validation Results:", MessageType.Info);
            
            if (validationResult.IsValid)
            {
                OutputViewModel.AddMessage("Document is valid!", MessageType.Success);
            }
            else
            {
                foreach (var error in validationResult.Errors)
                {
                    OutputViewModel.AddMessage($"Error: {error}", MessageType.Error);
                }
            }

            _logger.LogInformation("Document validation completed. Valid: {IsValid}, Errors: {ErrorCount}", 
                validationResult.IsValid, validationResult.Errors.Count);
        }
        catch (Exception ex)
        {
            ValidationStatus = "Validation Failed";
            OutputViewModel.AddMessage($"Validation failed: {ex.Message}", MessageType.Error);
            _logger.LogError(ex, "Error during document validation");
        }
    }

    [RelayCommand]
    private async Task ShowDataPoints()
    {
        if (CurrentDocument?.ReferenceDevice?.DataPointsList == null)
        {
            await _messageBoxService.ShowWarningAsync("No Data Points", "The current document does not contain data points.");
            return;
        }

        var dataPointsTab = OpenTabs.FirstOrDefault(t => t.Id == "datapoints");
        if (dataPointsTab == null)
        {
            var viewModel = new DataPointsViewModel(CurrentDocument.ReferenceDevice.DataPointsList);
            dataPointsTab = new TabItemViewModel
            {
                Id = "datapoints",
                Header = "Data Points",
                Content = new Views.DataPointsView { DataContext = viewModel }
            };
            OpenTabs.Add(dataPointsTab);
        }

        SelectedTab = dataPointsTab;
    }

    [RelayCommand]
    private void CloseTab(TabItemViewModel tab)
    {
        if (tab != null && OpenTabs.Contains(tab))
        {
            OpenTabs.Remove(tab);
            if (SelectedTab == tab && OpenTabs.Count > 0)
            {
                SelectedTab = OpenTabs[0];
            }
        }
    }

    [RelayCommand]
    private async Task OpenRecent(string filePath)
    {
        if (!await ConfirmUnsavedChanges()) return;
        await LoadDocument(filePath);
    }

    [RelayCommand]
    private void ShowPreferences()
    {
        // TODO: Implement preferences dialog
    }

    [RelayCommand]
    private void ShowDocumentation()
    {
        // TODO: Open documentation
    }

    [RelayCommand]
    private void ShowAbout()
    {
        // TODO: Show about dialog
    }

    [RelayCommand]
    private async Task ExitApplication()
    {
        if (await ConfirmUnsavedChanges())
        {
            Application.Current.Shutdown();
        }
    }

    private async Task LoadDocument(string filePath)
    {
        try
        {
            StatusMessage = "Loading document...";
            
            CurrentDocument = await _documentService.LoadDocumentAsync(filePath);
            CurrentFilePath = filePath;
            IsDocumentModified = false;
            
            NavigationViewModel.LoadDocument(CurrentDocument);
            AddToRecentFiles(filePath);
            OpenDefaultTabs();
            
            StatusMessage = "Document loaded successfully";
            DocumentStatus = Path.GetFileName(filePath);
            ValidationStatus = "Not Validated";
            
            _logger.LogInformation("Document loaded from {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading document from {FilePath}", filePath);
            await _messageBoxService.ShowErrorAsync("Error Loading Document", ex.Message);
            StatusMessage = "Error loading document";
        }
    }

    private async Task SaveDocumentToFile(string filePath)
    {
        if (CurrentDocument == null) return;

        try
        {
            StatusMessage = "Saving document...";
            
            await _documentService.SaveDocumentAsync(CurrentDocument, filePath);
            
            CurrentFilePath = filePath;
            IsDocumentModified = false;
            StatusMessage = "Document saved successfully";
            DocumentStatus = Path.GetFileName(filePath);
            
            AddToRecentFiles(filePath);
            
            _logger.LogInformation("Document saved to {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving document to {FilePath}", filePath);
            await _messageBoxService.ShowErrorAsync("Error Saving Document", ex.Message);
            StatusMessage = "Error saving document";
        }
    }

    private async Task<bool> ConfirmUnsavedChanges()
    {
        if (!IsDocumentModified) return true;

        var result = await _messageBoxService.ShowQuestionAsync(
            "Unsaved Changes",
            "The current document has unsaved changes. Do you want to save them?",
            MessageBoxButton.YesNoCancel);

        return result switch
        {
            MessageBoxResult.Yes => await TrySaveCurrentDocument(),
            MessageBoxResult.No => true,
            _ => false
        };
    }

    private async Task<bool> TrySaveCurrentDocument()
    {
        try
        {
            if (string.IsNullOrEmpty(CurrentFilePath))
            {
                await SaveAsDocument();
            }
            else
            {
                await SaveDocument();
            }
            return !IsDocumentModified;
        }
        catch
        {
            return false;
        }
    }

    private void OpenDefaultTabs()
    {
        OpenTabs.Clear();
        
        if (CurrentDocument?.ReferenceDevice?.Configuration != null)
        {
            var configTab = new TabItemViewModel
            {
                Id = "configuration",
                Header = "Device Configuration",
                Content = new Views.DeviceConfigurationView 
                { 
                    DataContext = new DeviceConfigurationViewModel(CurrentDocument.ReferenceDevice.Configuration) 
                }
            };
            OpenTabs.Add(configTab);
            SelectedTab = configTab;
        }
    }

    private void OnNavigationNodeSelected(object? sender, NavigationNodeSelectedEventArgs e)
    {
        // Update properties panel with selected node
        PropertiesViewModel.LoadProperties(e.SelectedNode);
    }

    private void UpdateWindowTitle()
    {
        var title = "DNP3 Device Profile Editor";
        
        if (!string.IsNullOrEmpty(CurrentFilePath))
        {
            title = $"{Path.GetFileName(CurrentFilePath)} - {title}";
        }
        else if (CurrentDocument != null)
        {
            title = $"New Document - {title}";
        }

        if (IsDocumentModified)
        {
            title = $"*{title}";
        }

        WindowTitle = title;
    }

    private void LoadRecentFiles()
    {
        // TODO: Load from settings
    }

    private void AddToRecentFiles(string filePath)
    {
        // TODO: Add to recent files and save to settings
    }

    private void CheckLicense()
    {
        if (!_licenseService.IsLicenseValid())
        {
            // TODO: Show license dialog
        }
    }

    partial void OnCurrentDocumentChanged(DNP3DeviceProfileDocument? value)
    {
        UpdateWindowTitle();
    }

    partial void OnCurrentFilePathChanged(string? value)
    {
        UpdateWindowTitle();
    }

    partial void OnIsDocumentModifiedChanged(bool value)
    {
        UpdateWindowTitle();
    }
}