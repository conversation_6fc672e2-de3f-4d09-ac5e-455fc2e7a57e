//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("networkConfigType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class NetworkConfigType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("portName")]
        public IpPortNameType PortName { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("typeOfEndPoint")]
        public TypeOfEndPointType TypeOfEndPoint { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("ipAddress")]
        public IpAddressType IpAddress { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("subnetMask")]
        public SubnetMaskType SubnetMask { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("gatewayIPAddress")]
        public GatewayIpAddressType GatewayIpAddress { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("tcpConnectionEstablishment")]
        public TcpConnectionEstablishmentType TcpConnectionEstablishment { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("ipAddressOfRemoteDevice")]
        public IpAddressOfRemoteDeviceType IpAddressOfRemoteDevice { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("tcpListenPort")]
        public TcpListenPortType TcpListenPort { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("tcpPortOfRemoteDevice")]
        public TcpPortOfRemoteDeviceType TcpPortOfRemoteDevice { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("tcpKeepAliveTimer")]
        public TcpKeepAliveTimerType TcpKeepAliveTimer { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("localUDPPort")]
        public LocalUdpPortType LocalUdpPort { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("destinationUDPPort")]
        public DestinationUdpPortType DestinationUdpPort { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("udpPortForUnsolicitedNullResponses")]
        public UdpPortForUnsolicitedNullResponsesType UdpPortForUnsolicitedNullResponses { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("udpPortForResponses")]
        public UdpPortForResponsesType UdpPortForResponses { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("multipleOutstationConnections")]
        public MultipleOutstationConnectionsType MultipleOutstationConnections { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("multipleMasterConnections")]
        public MultipleMasterConnectionsType MultipleMasterConnections { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("timeSynchronization")]
        public TimeSynchronizationType TimeSynchronization { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="NetworkConfigType" /> class.</para>
        /// </summary>
        public NetworkConfigType()
        {
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
    }
}
