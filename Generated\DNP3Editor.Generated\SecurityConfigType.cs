//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("securityConfigType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SecurityConfigType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("secureAuthenticationSupported")]
        public SecureAuthenticationSupportedType SecureAuthenticationSupported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxNumberUsers")]
        public MaxNumberUsersType MaxNumberUsers { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("securityResponseTimeout")]
        public SecurityResponseTimeoutType SecurityResponseTimeout { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("acceptsAggressiveMode")]
        public AcceptsAggressiveModeType AcceptsAggressiveMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("issuesAggressiveMode")]
        public IssuesAggressiveModeType IssuesAggressiveMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("sessionKeyChangeInterval")]
        public SessionKeyChangeIntervalType SessionKeyChangeInterval { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("sessionKeyChangeMessageCount")]
        public SessionKeyChangeMessageCountType SessionKeyChangeMessageCount { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("maxErrorCount")]
        public MaxErrorCountType MaxErrorCount { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("macAlgorithmRequested")]
        public MacAlgorithmRequestedType MacAlgorithmRequested { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("keyWrapAlgorithm")]
        public KeyWrapAlgorithmType KeyWrapAlgorithm { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("TLSCipherSuites")]
        public TlsCipherSuitesType TlsCipherSuites { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("changeCipherRequestTimeout")]
        public ChangeCipherRequestTimeoutType ChangeCipherRequestTimeout { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("numberCASupported")]
        public NumberCaSupportedType NumberCaSupported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("certificateRevocationCheckTime")]
        public CertificateRevocationCheckTimeType CertificateRevocationCheckTime { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("additionalCriticalFCs")]
        public AdditionalCriticalFCsType AdditionalCriticalFCs { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("otherCriticalFragments")]
        public OtherCriticalFragmentsType OtherCriticalFragments { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("remoteUpdateKeyChangeSupported")]
        public RemoteUpdateKeyChangeSupportedType RemoteUpdateKeyChangeSupported { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("permitUserCredentialExpiry")]
        public PermitUserCredentialExpiryType PermitUserCredentialExpiry { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="SecurityConfigType" /> class.</para>
        /// </summary>
        public SecurityConfigType()
        {
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
    }
}
