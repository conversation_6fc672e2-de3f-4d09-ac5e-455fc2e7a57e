//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("VirtualTerminalPointsTypeDataPoints", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class VirtualTerminalPointsTypeDataPoints
    {
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<VirtualTerminalPointType> _virtualTerminal;
        
        [System.Xml.Serialization.XmlElementAttribute("virtualTerminal")]
        public System.Collections.ObjectModel.Collection<VirtualTerminalPointType> VirtualTerminal
        {
            get
            {
                return _virtualTerminal;
            }
            private set
            {
                _virtualTerminal = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the VirtualTerminal collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool VirtualTerminalSpecified
        {
            get
            {
                return (this.VirtualTerminal.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="VirtualTerminalPointsTypeDataPoints" /> class.</para>
        /// </summary>
        public VirtualTerminalPointsTypeDataPoints()
        {
            this._virtualTerminal = new System.Collections.ObjectModel.Collection<VirtualTerminalPointType>();
        }
    }
}
