//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("DatasetDescriptorListTypeDescriptor", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DatasetDescriptorListTypeDescriptor
    {
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("id")]
        public uint Id { get; set; }
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("description")]
        public string Description { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("properties")]
        public DatasetPropertiesType Properties { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("changeEventClass")]
        public EventAssignedClassType ChangeEventClass { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("class0ResponseMode")]
        public DatasetClass0ResponseModeType Class0ResponseMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("datasetElements")]
        public DatasetDescriptorListTypeDescriptorDatasetElements DatasetElements { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<DatasetDescriptorListTypeDescriptorPointIndexAttribute> _pointIndexAttribute;
        
        [System.Xml.Serialization.XmlElementAttribute("pointIndexAttribute")]
        public System.Collections.ObjectModel.Collection<DatasetDescriptorListTypeDescriptorPointIndexAttribute> PointIndexAttribute
        {
            get
            {
                return _pointIndexAttribute;
            }
            private set
            {
                _pointIndexAttribute = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the PointIndexAttribute collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PointIndexAttributeSpecified
        {
            get
            {
                return (this.PointIndexAttribute.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="DatasetDescriptorListTypeDescriptor" /> class.</para>
        /// </summary>
        public DatasetDescriptorListTypeDescriptor()
        {
            this._pointIndexAttribute = new System.Collections.ObjectModel.Collection<DatasetDescriptorListTypeDescriptorPointIndexAttribute>();
        }
    }
}
