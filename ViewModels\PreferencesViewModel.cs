using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace DNP3Editor.ViewModels;

public partial class PreferencesViewModel : ObservableObject
{
    [ObservableProperty]
    private bool autoSaveEnabled = true;

    [ObservableProperty]
    private int autoSaveInterval = 5;

    [ObservableProperty]
    private bool validateOnSave = true;

    [ObservableProperty]
    private bool showLineNumbers = true;

    [ObservableProperty]
    private string defaultFileLocation = "";

    [ObservableProperty]
    private bool enableLogging = true;

    [ObservableProperty]
    private string logLevel = "Information";

    public string[] LogLevels { get; } = { "Trace", "Debug", "Information", "Warning", "Error", "Critical" };

    [RelayCommand]
    private void SavePreferences()
    {
        // TODO: Save preferences to settings
    }

    [RelayCommand]
    private void ResetToDefaults()
    {
        AutoSaveEnabled = true;
        AutoSaveInterval = 5;
        ValidateOnSave = true;
        ShowLineNumbers = true;
        DefaultFileLocation = "";
        EnableLogging = true;
        LogLevel = "Information";
    }

    [RelayCommand]
    private void BrowseDefaultLocation()
    {
        // TODO: Show folder browser dialog
    }
}
