//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("pointType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogInputPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AnalogOutputPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryInputPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BinaryOutputPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CounterPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DoubleBitInputPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(InputPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OctetStringPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(OutputPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(StringPointType))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(VirtualTerminalPointType))]
    public partial class PointType
    {
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("index")]
        public string Index { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("name")]
        public string Name { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("description")]
        public string Description { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="PointType" /> class.</para>
        /// </summary>
        public PointType()
        {
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
    }
}
