//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("AnalogInputPointsTypeDataPoints", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class AnalogInputPointsTypeDataPoints
    {
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<AnalogInputPointType> _analogInput;
        
        [System.Xml.Serialization.XmlElementAttribute("analogInput")]
        public System.Collections.ObjectModel.Collection<AnalogInputPointType> AnalogInput
        {
            get
            {
                return _analogInput;
            }
            private set
            {
                _analogInput = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the AnalogInput collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AnalogInputSpecified
        {
            get
            {
                return (this.AnalogInput.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="AnalogInputPointsTypeDataPoints" /> class.</para>
        /// </summary>
        public AnalogInputPointsTypeDataPoints()
        {
            this._analogInput = new System.Collections.ObjectModel.Collection<AnalogInputPointType>();
        }
    }
}
