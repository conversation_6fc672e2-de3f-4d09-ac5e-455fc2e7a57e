//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("BinaryInputGroupTypeConfiguration", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class BinaryInputGroupTypeConfiguration
    {
        
        [System.Xml.Serialization.XmlElementAttribute("defaultStaticVariation")]
        public DefaultBinaryInputStaticVariationType DefaultStaticVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("defaultEventVariation")]
        public DefaultBinaryInputEventVariationType DefaultEventVariation { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("eventReportingMode")]
        public EventReportingModeType EventReportingMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("class0ResponseMode")]
        public Class0ResponseModeType Class0ResponseMode { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("eventBufferPerObjectGroup")]
        public EventBufferPerObjectGroupType EventBufferPerObjectGroup { get; set; }
    }
}
