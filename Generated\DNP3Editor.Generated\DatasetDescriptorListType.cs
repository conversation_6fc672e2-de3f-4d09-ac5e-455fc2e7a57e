//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("datasetDescriptorListType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DatasetDescriptorListType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("configuration")]
        public DatasetDescriptorListTypeConfiguration Configuration { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<DatasetDescriptorListTypeDescriptor> _descriptor;
        
        [System.Xml.Serialization.XmlElementAttribute("descriptor")]
        public System.Collections.ObjectModel.Collection<DatasetDescriptorListTypeDescriptor> Descriptor
        {
            get
            {
                return _descriptor;
            }
            private set
            {
                _descriptor = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Descriptor collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DescriptorSpecified
        {
            get
            {
                return (this.Descriptor.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="DatasetDescriptorListType" /> class.</para>
        /// </summary>
        public DatasetDescriptorListType()
        {
            this._descriptor = new System.Collections.ObjectModel.Collection<DatasetDescriptorListTypeDescriptor>();
            this._datasetPoint = new System.Collections.ObjectModel.Collection<DatasetDescriptorListTypeDatasetPointDnpData>();
            this._userData = new System.Collections.ObjectModel.Collection<UserDataType>();
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<DatasetDescriptorListTypeDatasetPointDnpData> _datasetPoint;
        
        [System.Xml.Serialization.XmlArrayAttribute("datasetPoint")]
        [System.Xml.Serialization.XmlArrayItemAttribute("dnpData", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
        public System.Collections.ObjectModel.Collection<DatasetDescriptorListTypeDatasetPointDnpData> DatasetPoint
        {
            get
            {
                return _datasetPoint;
            }
            private set
            {
                _datasetPoint = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the DatasetPoint collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DatasetPointSpecified
        {
            get
            {
                return (this.DatasetPoint.Count != 0);
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<UserDataType> _userData;
        
        [System.Xml.Serialization.XmlElementAttribute("userData")]
        public System.Collections.ObjectModel.Collection<UserDataType> UserData
        {
            get
            {
                return _userData;
            }
            private set
            {
                _userData = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the UserData collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserDataSpecified
        {
            get
            {
                return (this.UserData.Count != 0);
            }
        }
    }
}
