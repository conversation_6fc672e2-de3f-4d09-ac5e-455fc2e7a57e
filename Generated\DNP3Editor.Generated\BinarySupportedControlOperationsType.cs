//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("binarySupportedControlOperationsType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class BinarySupportedControlOperationsType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("supportSelectOperate")]
        public EmptyElement SupportSelectOperate { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportDirectOperate")]
        public EmptyElement SupportDirectOperate { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportDirectOperateNoAck")]
        public EmptyElement SupportDirectOperateNoAck { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportPulseOn")]
        public EmptyElement SupportPulseOn { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportPulseOff")]
        public EmptyElement SupportPulseOff { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportLatchOn")]
        public EmptyElement SupportLatchOn { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportLatchOff")]
        public EmptyElement SupportLatchOff { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportTrip")]
        public EmptyElement SupportTrip { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("supportClose")]
        public EmptyElement SupportClose { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("countGreaterThanOne")]
        public EmptyElement CountGreaterThanOne { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("cancelCurrentOperation")]
        public EmptyElement CancelCurrentOperation { get; set; }
    }
}
