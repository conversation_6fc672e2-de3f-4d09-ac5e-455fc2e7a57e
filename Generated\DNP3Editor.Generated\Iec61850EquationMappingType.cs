//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("iec61850EquationMappingType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class Iec61850EquationMappingType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("note")]
        public string Note { get; set; }
        
        [System.ComponentModel.DataAnnotations.RequiredAttribute(AllowEmptyStrings=true)]
        [System.Xml.Serialization.XmlElementAttribute("equation")]
        public string Equation { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<Dnp3XPathType> _dnp3XPath;
        
        [System.Xml.Serialization.XmlElementAttribute("dnp3XPath")]
        public System.Collections.ObjectModel.Collection<Dnp3XPathType> Dnp3XPath
        {
            get
            {
                return _dnp3XPath;
            }
            private set
            {
                _dnp3XPath = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Dnp3XPath collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool Dnp3XPathSpecified
        {
            get
            {
                return (this.Dnp3XPath.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="Iec61850EquationMappingType" /> class.</para>
        /// </summary>
        public Iec61850EquationMappingType()
        {
            this._dnp3XPath = new System.Collections.ObjectModel.Collection<Dnp3XPathType>();
            this._iec61850Path = new System.Collections.ObjectModel.Collection<Iec61850PathType>();
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<Iec61850PathType> _iec61850Path;
        
        [System.Xml.Serialization.XmlElementAttribute("iec61850Path")]
        public System.Collections.ObjectModel.Collection<Iec61850PathType> Iec61850Path
        {
            get
            {
                return _iec61850Path;
            }
            private set
            {
                _iec61850Path = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Iec61850Path collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool Iec61850PathSpecified
        {
            get
            {
                return (this.Iec61850Path.Count != 0);
            }
        }
    }
}
