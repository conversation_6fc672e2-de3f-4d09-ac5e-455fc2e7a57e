//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("numberOfEventsType", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class NumberOfEventsType
    {
        
        [System.Xml.Serialization.XmlElementAttribute("fixed")]
        public string Fixed { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("range")]
        public NonNegativeIntRangeType Range { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<string> _selectable;
        
        [System.Xml.Serialization.XmlElementAttribute("selectable")]
        public System.Collections.ObjectModel.Collection<string> Selectable
        {
            get
            {
                return _selectable;
            }
            private set
            {
                _selectable = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Selectable collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SelectableSpecified
        {
            get
            {
                return (this.Selectable.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="NumberOfEventsType" /> class.</para>
        /// </summary>
        public NumberOfEventsType()
        {
            this._selectable = new System.Collections.ObjectModel.Collection<string>();
            this._configurableOther = new System.Collections.ObjectModel.Collection<ConfigurableCustomType>();
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<ConfigurableCustomType> _configurableOther;
        
        [System.Xml.Serialization.XmlElementAttribute("configurableOther")]
        public System.Collections.ObjectModel.Collection<ConfigurableCustomType> ConfigurableOther
        {
            get
            {
                return _configurableOther;
            }
            private set
            {
                _configurableOther = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the ConfigurableOther collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ConfigurableOtherSpecified
        {
            get
            {
                return (this.ConfigurableOther.Count != 0);
            }
        }
    }
}
