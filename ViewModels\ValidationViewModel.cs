using CommunityToolkit.Mvvm.ComponentModel;
using DNP3Editor.Services;
using DNP3Editor.Models;
using DNP3Editor.Events;
using DNP3Editor.Generated;

namespace DNP3Editor.ViewModels;

public partial class ValidationViewModel : ObservableObject
{
    private readonly IValidationService _validationService;

    [ObservableProperty]
    private string validationStatus = "Not Validated";

    [ObservableProperty]
    private bool isValidating;

    public event EventHandler<ValidationCompletedEventArgs>? ValidationCompleted;

    public ValidationViewModel(IValidationService validationService)
    {
        _validationService = validationService;
    }

    public async Task ValidateDocumentAsync(Dnp3DeviceProfileDocument document)
    {
        IsValidating = true;
        ValidationStatus = "Validating...";

        try
        {
            var result = await _validationService.ValidateDocumentAsync(document);
            
            ValidationStatus = result.IsValid 
                ? "Valid" 
                : $"Errors: {result.Errors.Count}";

            ValidationCompleted?.Invoke(this, new ValidationCompletedEventArgs(result));
        }
        catch (Exception ex)
        {
            ValidationStatus = "Validation Failed";
            var errorResult = new ValidationResult
            {
                IsValid = false,
                Errors = new List<string> { ex.Message }
            };
            ValidationCompleted?.Invoke(this, new ValidationCompletedEventArgs(errorResult));
        }
        finally
        {
            IsValidating = false;
        }
    }
}
