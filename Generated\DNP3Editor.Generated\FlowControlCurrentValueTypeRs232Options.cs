//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// This code was generated by XmlSchemaClassGenerator version 2.1.1184.0 using the following command:
// XmlSchemaClassGenerator.Console --namespace DNP3Editor.Generated --output Generated --sf --verbose DNP3DeviceProfileNovember2014.xsd
namespace DNP3Editor.Generated
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("XmlSchemaClassGenerator", "2.1.1184.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute("FlowControlCurrentValueTypeRs232Options", Namespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014", AnonymousType=true)]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class FlowControlCurrentValueTypeRs232Options
    {
        
        [System.Xml.Serialization.XmlElementAttribute("assertsRTSBeforeTx")]
        public EmptyElement AssertsRtsBeforeTx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("assertsDTRBeforeTx")]
        public EmptyElement AssertsDtrBeforeTx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("assertsRTSBeforeRx")]
        public EmptyElement AssertsRtsBeforeRx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("assertsDTRBeforeRx")]
        public EmptyElement AssertsDtrBeforeRx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("alwaysAssertsRTS")]
        public EmptyElement AlwaysAssertsRts { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("alwaysAssertsDTR")]
        public EmptyElement AlwaysAssertsDtr { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requiresCTSBeforeTx")]
        public AssertedCurrentValueType RequiresCtsBeforeTx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requiresDCDBeforeTx")]
        public AssertedCurrentValueType RequiresDcdBeforeTx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requiresDSRBeforeTx")]
        public AssertedCurrentValueType RequiresDsrBeforeTx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requiresRIBeforeTx")]
        public AssertedCurrentValueType RequiresRiBeforeTx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("rxInactive")]
        public EmptyElement RxInactive { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requiresCTSBeforeRx")]
        public AssertedCurrentValueType RequiresCtsBeforeRx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requiresDCDBeforeRx")]
        public AssertedCurrentValueType RequiresDcdBeforeRx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requiresDSRBeforeRx")]
        public AssertedCurrentValueType RequiresDsrBeforeRx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("requiresRIBeforeRx")]
        public AssertedCurrentValueType RequiresRiBeforeRx { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("alwaysIgnoresCTS")]
        public EmptyElement AlwaysIgnoresCts { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("alwaysIgnoresDCD")]
        public EmptyElement AlwaysIgnoresDcd { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("alwaysIgnoresDSR")]
        public EmptyElement AlwaysIgnoresDsr { get; set; }
        
        [System.Xml.Serialization.XmlElementAttribute("alwaysIgnoresRI")]
        public EmptyElement AlwaysIgnoresRi { get; set; }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        private System.Collections.ObjectModel.Collection<CustomType> _other;
        
        [System.Xml.Serialization.XmlElementAttribute("other")]
        public System.Collections.ObjectModel.Collection<CustomType> Other
        {
            get
            {
                return _other;
            }
            private set
            {
                _other = value;
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Gets a value indicating whether the Other collection is empty.</para>
        /// </summary>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OtherSpecified
        {
            get
            {
                return (this.Other.Count != 0);
            }
        }
        
        /// <summary>
        /// <para xml:lang="en">Initializes a new instance of the <see cref="FlowControlCurrentValueTypeRs232Options" /> class.</para>
        /// </summary>
        public FlowControlCurrentValueTypeRs232Options()
        {
            this._other = new System.Collections.ObjectModel.Collection<CustomType>();
        }
    }
}
